<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>无人机任务规划实验系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .feature-icon {
            font-size: 2.5rem;
            color: #0d6efd;
            margin-bottom: 1rem;
        }
        .hero-section {
            background-color: #f8f9fa;
            padding: 5rem 0;
            margin-bottom: 3rem;
        }
        .card {
            transition: transform 0.3s;
            margin-bottom: 2rem;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">无人机任务规划实验系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/experiments">实验管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/analysis">数据分析</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/settings">系统设置</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main>
        <!-- 英雄区域 -->
        <section class="hero-section">
            <div class="container text-center">
                <h1 class="display-4 mb-4">无人机任务规划实验系统</h1>
                <p class="lead mb-5">一个用于处理和记录无人机任务规划实验数据的综合平台，提供直观的用户界面和强大的数据分析功能。</p>

                <!-- 实验记录选择 -->
                <div class="row justify-content-center mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">选择实验记录</h5>
                            </div>
                            <div class="card-body">
                                {% if records %}
                                    <form id="recordSelectForm">
                                        <div class="mb-3">
                                            <select class="form-select" id="recordSelect" name="record_path">
                                                <option value="">创建新记录</option>
                                                {% for record in records %}
                                                    <option value="{{ record.path }}">{{ record.display_name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <button type="button" id="selectRecordBtn" class="btn btn-success">确认选择</button>
                                    </form>
                                {% else %}
                                    <div class="alert alert-info mb-0">
                                        <i class="bi bi-info-circle-fill me-2"></i>
                                        暂无实验记录，将创建新记录。
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-center gap-3">
                    <a href="/experiments" class="btn btn-primary btn-lg">开始实验</a>
                    <a href="/analysis" class="btn btn-outline-primary btn-lg">查看分析</a>
                </div>
            </div>
        </section>

        <!-- 功能特点 -->
        <section class="container mb-5">
            <h2 class="text-center mb-5">系统功能</h2>
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="card h-100 text-center p-4">
                        <div class="card-body">
                            <i class="bi bi-gear-fill feature-icon"></i>
                            <h3 class="card-title">简化实验流程</h3>
                            <p class="card-text">直观的用户界面，替代繁琐的命令行操作，提供实验环境和任务的可视化浏览和选择功能，实现一键式处理和分析。</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card h-100 text-center p-4">
                        <div class="card-body">
                            <i class="bi bi-clipboard-data-fill feature-icon"></i>
                            <h3 class="card-title">增强数据记录</h3>
                            <p class="card-text">用户友好的表单来记录人工规划时间和专家评分，提供实时数据验证和反馈机制，支持批量数据导入导出。</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card h-100 text-center p-4">
                        <div class="card-body">
                            <i class="bi bi-graph-up feature-icon"></i>
                            <h3 class="card-title">数据分析可视化</h3>
                            <p class="card-text">图表和统计视图对比LLM规划时间与人工规划时间，专家评分的分布和趋势分析，任务类型和可行性的统计分析。</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 快速入门 -->
        <section class="container mb-5">
            <h2 class="text-center mb-5">快速入门</h2>
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h3>1. 实验管理</h3>
                    <p>浏览和选择实验环境，一键处理实验数据，查看任务分析结果。</p>
                    <h3>2. 数据记录</h3>
                    <p>记录人工规划时间和专家评分，对比LLM与人工规划效率。</p>
                    <h3>3. 数据分析</h3>
                    <p>通过图表和统计视图分析实验结果，导出数据进行深入研究。</p>
                    <a href="/experiments" class="btn btn-primary mt-3">开始使用</a>
                </div>
                <div class="col-md-6">
                    <img src="https://via.placeholder.com/600x400?text=系统界面预览" alt="系统界面预览" class="img-fluid rounded shadow">
                </div>
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">© 2025 无人机任务规划实验系统 | 版权所有</p>
        </div>
    </footer>

    <!-- 结果提示 -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="resultToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <strong class="me-auto">操作结果</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body" id="toastMessage"></div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const selectRecordBtn = document.getElementById('selectRecordBtn');
            if (selectRecordBtn) {
                const resultToast = new bootstrap.Toast(document.getElementById('resultToast'));
                const toastMessage = document.getElementById('toastMessage');

                selectRecordBtn.addEventListener('click', function() {
                    const form = document.getElementById('recordSelectForm');
                    const formData = new FormData(form);

                    fetch('/select_record', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            toastMessage.textContent = data.message;
                            toastMessage.className = 'toast-body text-success';

                            // 成功选择记录后，跳转到实验页面
                            setTimeout(() => {
                                window.location.href = '/experiments';
                            }, 1500);
                        } else {
                            toastMessage.textContent = data.message;
                            toastMessage.className = 'toast-body text-danger';
                        }
                        resultToast.show();
                    })
                    .catch(error => {
                        toastMessage.textContent = '操作失败: ' + error.message;
                        toastMessage.className = 'toast-body text-danger';
                        resultToast.show();
                    });
                });
            }
        });
    </script>
</body>
</html>
