#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试实验数据处理和记录系统
"""

import os
import sys
import json
from experiment_manager import ExperimentManager

def test_summary():
    """测试获取实验摘要"""
    print("测试获取实验摘要...")
    manager = ExperimentManager()
    summary = manager.get_experiment_summary()
    print(json.dumps(summary, indent=2, ensure_ascii=False))

def test_process_single_experiment(env_id="1"):
    """测试处理单个实验"""
    print(f"测试处理环境 {env_id}...")
    manager = ExperimentManager()
    result = manager.process_single_experiment(env_id)
    print(f"处理结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
    
    # 获取摘要
    summary = manager.get_experiment_summary()
    print(f"处理后的摘要: {json.dumps(summary, indent=2, ensure_ascii=False)}")
    
    # 导出数据
    json_path = manager.export_data("json")
    csv_path = manager.export_data("csv")
    print(f"数据已导出到: {json_path} 和 {csv_path}")

def test_record_manual_time_and_score():
    """测试记录人工规划时间和专家评分"""
    print("测试记录人工规划时间和专家评分...")
    manager = ExperimentManager()
    
    # 记录人工规划时间
    manager.record_manual_planning_time("1", "1", 120.5)
    print("已记录人工规划时间")
    
    # 记录专家评分
    manager.record_expert_score("1", "1", 8)
    print("已记录专家评分")
    
    # 获取摘要
    summary = manager.get_experiment_summary()
    print(f"记录后的摘要: {json.dumps(summary, indent=2, ensure_ascii=False)}")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python test_experiment_system.py [summary|process|record]")
        print("  summary: 测试获取实验摘要")
        print("  process <env_id>: 测试处理单个实验，默认为环境1")
        print("  record: 测试记录人工规划时间和专家评分")
        return
    
    command = sys.argv[1]
    
    if command == "summary":
        test_summary()
    elif command == "process":
        env_id = sys.argv[2] if len(sys.argv) > 2 else "1"
        test_process_single_experiment(env_id)
    elif command == "record":
        test_record_manual_time_and_score()
    else:
        print(f"未知命令: {command}")

if __name__ == "__main__":
    main()
