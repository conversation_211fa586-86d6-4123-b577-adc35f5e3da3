import math
import json
from enum import Enum
from typing import List, Dict, Tuple, Any, Optional
from dataclasses import dataclass
from camera_calculate import CameraCalc


class CoordType(Enum):
    """定义航点类型"""
    SURVEY_ENTRY = 0    # 测量入口点
    SURVEY_EXIT = 1     # 测量出口点
    INTERIOR = 2        # 内部点
    TURNAROUND = 3      # 转弯点

class MavFrame(Enum):
    """MAV坐标系常量"""
    GLOBAL_RELATIVE_ALT = 3
    MISSION = 2

class MavCmd(Enum):
    """MAV命令常量"""
    NAV_WAYPOINT = 16
    IMAGE_START_CAPTURE = 2000
    DO_SET_CAM_TRIGG_DIST = 206


def calculate_distance(coord1, coord2):
    """计算两个坐标之间的距离（米）"""
    # 使用简单的Haversine公式
    lat1, lon1 = coord1[0], coord1[1]
    lat2, lon2 = coord2[0], coord2[1]
    
    # 转换为弧度
    lat1, lon1 = math.radians(lat1), math.radians(lon1)
    lat2, lon2 = math.radians(lat2), math.radians(lon2)
    
    # Haversine公式
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
    earth_radius = 6371000  # 米
    distance = earth_radius * c
    
    # 如果有高度信息，加上高度差
    if len(coord1) > 2 and len(coord2) > 2:
        alt1, alt2 = coord1[2], coord2[2]
        distance = math.sqrt(distance**2 + (alt2 - alt1)**2)
    
    return distance


def calculate_azimuth(coord1, coord2):
    """计算两个坐标之间的方位角（度）"""
    lat1, lon1 = math.radians(coord1[0]), math.radians(coord1[1])
    lat2, lon2 = math.radians(coord2[0]), math.radians(coord2[1])
    
    dlon = lon2 - lon1
    y = math.sin(dlon) * math.cos(lat2)
    x = math.cos(lat1) * math.sin(lat2) - math.sin(lat1) * math.cos(lat2) * math.cos(dlon)
    
    azimuth = math.atan2(y, x)
    azimuth_degrees = math.degrees(azimuth)
    if azimuth_degrees < 0:
        azimuth_degrees += 360
    
    return azimuth_degrees


def calculate_coordinate_at_distance_and_azimuth(coord, distance, azimuth):
    """计算给定坐标在指定距离和方位角上的新坐标"""
    # 转换为弧度
    lat1, lon1 = math.radians(coord[0]), math.radians(coord[1])
    azimuth = math.radians(azimuth)
    
    # 计算新位置
    earth_radius = 6371000  # 米
    
    # 计算新纬度
    lat2 = math.asin(math.sin(lat1) * math.cos(distance/earth_radius) + 
                     math.cos(lat1) * math.sin(distance/earth_radius) * math.cos(azimuth))
    
    # 计算新经度
    lon2 = lon1 + math.atan2(math.sin(azimuth) * math.sin(distance/earth_radius) * math.cos(lat1), 
                             math.cos(distance/earth_radius) - math.sin(lat1) * math.sin(lat2))
    
    # 转换回度
    lat2_deg = math.degrees(lat2)
    lon2_deg = math.degrees(lon2)
    
    # 返回新坐标，保留原高度（如果有）
    if len(coord) > 2:
        return (lat2_deg, lon2_deg, coord[2])
    else:
        return (lat2_deg, lon2_deg)


def latlon_to_mercator(lat, lon):
    """
    将经纬度坐标转换为墨卡托投影坐标
    
    参数:
        lat: 纬度（度）
        lon: 经度（度）
        
    返回:
        (x, y): 墨卡托投影坐标（米）
    """
    # 地球半径（米）
    earth_radius = 6378137.0
    
    # 将经纬度转换为弧度
    lat_rad = math.radians(lat)
    lon_rad = math.radians(lon)
    
    # 计算墨卡托投影坐标
    x = earth_radius * lon_rad
    y = earth_radius * math.log(math.tan(math.pi/4 + lat_rad/2))
    
    return (x, y)

def mercator_to_latlon(x, y):
    """
    将墨卡托投影坐标转换为经纬度坐标
    
    参数:
        x: 墨卡托 x 坐标（米）
        y: 墨卡托 y 坐标（米）
        
    返回:
        (lat, lon): 经纬度坐标（度）
    """
    # 地球半径（米）
    earth_radius = 6378137.0
    
    # 计算经纬度（弧度）
    lon_rad = x / earth_radius
    lat_rad = 2 * math.atan(math.exp(y / earth_radius)) - math.pi/2
    
    # 转换为度
    lat = math.degrees(lat_rad)
    lon = math.degrees(lon_rad)
    
    return (lat, lon)

@dataclass
class Coordinate:
    """坐标类，存储航点的经纬度和高度"""
    latitude: float
    longitude: float
    altitude: float
    
class MissionItem:
    """任务项类，表示任务计划中的一个操作项"""
    def __init__(self, seq_num, command, frame, param1, param2, param3, param4, 
                 latitude, longitude, altitude, auto_continue=True, is_current=False):
        self.seq_num = seq_num
        self.command = command
        self.frame = frame
        self.param1 = param1
        self.param2 = param2
        self.param3 = param3
        self.param4 = param4
        self.latitude = latitude
        self.longitude = longitude
        self.altitude = altitude
        self.auto_continue = auto_continue
        self.is_current = is_current
    
    def __str__(self):
        """格式化输出任务项信息"""
        return f"MissionItem({self.seq_num}, {self.command}, {self.frame}, {self.param1}, {self.param2}, " \
               f"{self.param3}, {self.param4}, {self.latitude}, {self.longitude}, {self.altitude})"
    
    def to_dict(self):
        """将任务项转换为字典，用于JSON序列化"""
        return {
            "autoContinue": self.auto_continue,
            "command": self.command.value,
            "doJumpId": self.seq_num,
            "frame": self.frame.value,
            "params": [
                self.param1,
                self.param2,
                self.param3,
                self.param4,
                self.latitude,
                self.longitude,
                self.altitude
            ],
            "type": "SimpleItem"
        }

def offset_polyline(polyline, offset_distance):
    """
    改进的折线偏移函数，特别处理拐角以确保整个偏移线保持平行
    
    参数:
        polyline: [lat, lon] 或 [lat, lon, alt] 坐标列表
        offset_distance: 偏移距离（米），正值向右偏移，负值向左偏移
        
    返回:
        偏移后的新折线
    """
    if len(polyline) < 2:
        return polyline.copy()
    
    # 提取高度信息（如果有）
    has_altitude = len(polyline[0]) > 2
    altitudes = [point[2] if has_altitude else None for point in polyline]
    
    # 将经纬度转换为墨卡托投影
    mercator_points = [latlon_to_mercator(point[0], point[1]) for point in polyline]
    
    # 计算每个线段的方向和法向量
    segments = []
    for i in range(len(mercator_points) - 1):
        p1 = mercator_points[i]
        p2 = mercator_points[i+1]
        
        # 计算线段方向向量
        dx = p2[0] - p1[0]
        dy = p2[1] - p1[1]
        
        length = math.sqrt(dx**2 + dy**2)
        if length < 1e-6:  # 防止除以零
            segments.append({
                'p1': p1,
                'p2': p2,
                'direction': (0, 0),
                'normal': (0, 0),
                'length': 0
            })
        else:
            # 单位方向向量
            direction = (dx / length, dy / length)
            
            # 法线向量（垂直于方向）
            normal = (-direction[1], direction[0])
            
            # 根据偏移方向调整法线
            if offset_distance < 0:
                normal = (-normal[0], -normal[1])
            
            segments.append({
                'p1': p1,
                'p2': p2,
                'direction': direction,
                'normal': normal,
                'length': length
            })
    
    # 创建偏移后的点列表
    offset_mercator_points = []
    
    # 处理第一个点
    first_segment = segments[0]
    offset_mercator_points.append((
        first_segment['p1'][0] + first_segment['normal'][0] * abs(offset_distance),
        first_segment['p1'][1] + first_segment['normal'][1] * abs(offset_distance)
    ))
    
    # 处理中间拐角点
    for i in range(1, len(mercator_points) - 1):
        prev_segment = segments[i-1]
        curr_segment = segments[i]
        
        # 如果是无效的零长度线段，跳过
        if prev_segment['length'] < 1e-6 or curr_segment['length'] < 1e-6:
            continue
        
        # 计算两个线段法向量的点积，判断是内角还是外角
        dot_product = (prev_segment['normal'][0] * curr_segment['normal'][0] + 
                       prev_segment['normal'][1] * curr_segment['normal'][1])
        
        # 计算两个偏移线段的交点
        p1_offset = (
            prev_segment['p2'][0] + prev_segment['normal'][0] * abs(offset_distance),
            prev_segment['p2'][1] + prev_segment['normal'][1] * abs(offset_distance)
        )
        
        p2_offset = (
            curr_segment['p1'][0] + curr_segment['normal'][0] * abs(offset_distance),
            curr_segment['p1'][1] + curr_segment['normal'][1] * abs(offset_distance)
        )
        
        # 对于平行或几乎平行的线段，直接使用第二个偏移点
        if abs(dot_product - 1.0) < 1e-6:
            offset_mercator_points.append(p2_offset)
            continue
        
        # 计算两条线的交点
        try:
            # 通过求解参数方程计算交点
            # 第一条线: p1_offset + t1 * prev_direction
            # 第二条线: p2_offset + t2 * curr_direction
            prev_direction = prev_segment['direction']
            curr_direction = curr_segment['direction']
            
            # 构建线性方程组
            # p1_offset.x + t1 * prev_direction.x = p2_offset.x + t2 * curr_direction.x
            # p1_offset.y + t1 * prev_direction.y = p2_offset.y + t2 * curr_direction.y
            
            # 用克莱默法则求解
            det = prev_direction[0] * (-curr_direction[1]) - (-curr_direction[0]) * prev_direction[1]
            
            if abs(det) < 1e-10:  # 平行或几乎平行
                # 使用中点作为折线节点
                offset_mercator_points.append((
                    (p1_offset[0] + p2_offset[0]) / 2,
                    (p1_offset[1] + p2_offset[1]) / 2
                ))
            else:
                # 求解t1
                dx = p2_offset[0] - p1_offset[0]
                dy = p2_offset[1] - p1_offset[1]
                t1 = (dx * (-curr_direction[1]) - (-curr_direction[0]) * dy) / det
                
                # 计算交点
                intersection = (
                    p1_offset[0] + t1 * prev_direction[0],
                    p1_offset[1] + t1 * prev_direction[1]
                )
                
                # 添加交点
                offset_mercator_points.append(intersection)
                
        except Exception:
            # 如果计算失败，使用简单的中点连接
            offset_mercator_points.append((
                (p1_offset[0] + p2_offset[0]) / 2,
                (p1_offset[1] + p2_offset[1]) / 2
            ))
    
    # 处理最后一个点
    last_segment = segments[-1]
    offset_mercator_points.append((
        last_segment['p2'][0] + last_segment['normal'][0] * abs(offset_distance),
        last_segment['p2'][1] + last_segment['normal'][1] * abs(offset_distance)
    ))
    
    # 将墨卡托投影转回经纬度
    result = []
    for i, point in enumerate(offset_mercator_points):
        lat_lon = mercator_to_latlon(point[0], point[1])
        
        # 添加高度信息（如果有）
        if has_altitude:
            # 为每个点找到最接近的原始点的高度
            alt_index = min(i, len(altitudes) - 1)
            result.append((lat_lon[0], lat_lon[1], altitudes[alt_index]))
        else:
            result.append(lat_lon)
    
    return result


class CorridorScanComplexItem:
    def __init__(self, corridor_polyline, camera_calc, corridor_width, turn_around_distance=5, 
                 entry_point=0, camera_trigger_in_turn_around=False, vehicle_speed=5):
        """
        初始化走廊扫描复杂项
        
        参数:
            corridor_polyline: 定义走廊中心线的[lat, lon]或[lat, lon, alt]坐标列表
            camera_calc: 包含相机参数的字典
            corridor_width: 走廊宽度（米）
            turn_around_distance: 转弯距离（米）
            entry_point: 入口点选择(0-3)
            camera_trigger_in_turn_around: 是否在转弯区域触发相机
            vehicle_speed: 飞行器速度（米/秒）
        """
        self.corridor_polyline = corridor_polyline
        self.camera_calc = camera_calc
        self.corridor_width = corridor_width
        self.turn_around_distance = turn_around_distance
        self.entry_point = entry_point
        self.camera_trigger_in_turn_around = camera_trigger_in_turn_around
        self.vehicle_speed = vehicle_speed
        
        # 计算的值
        self.transects = []
        self.survey_area_polygon = []
        self.camera_shots = 0
        self.complex_distance = 0
        
        # 任务生成相关的参数
        self.sequence_number = 1
        self.trigger_camera_enabled = True  # 是否启用相机触发
        self.hover_and_capture_enabled = False  # 走廊扫描不使用悬停拍照模式
        self.trigger_distance_value = self.camera_calc.get("AdjustedFootprintFrontal", 10.0)  # 相机触发距离
        
        # 初始化
        self._rebuild_corridor_polygon()
        self._rebuild_transects()
        self._recalc_complex_distance()
        self._recalc_camera_shots()
    
    def _calc_transect_spacing(self):
        """根据相机参数计算飞行条带之间的间距"""
        transect_spacing = self.camera_calc.get("AdjustedFootprintSide", 0)
        if transect_spacing < 0.5:
            # 我们不能让间距太小，限制为最小0.5米
            # 或设置为大值，这将导致只有一条飞行带
            transect_spacing = 100000
        
        return transect_spacing
    
    def _calc_transect_count(self):
        """计算需要的飞行条带数量"""
        full_width = self.corridor_width
        transect_spacing = self._calc_transect_spacing()
        
        return math.ceil(full_width / transect_spacing) if full_width > 0 else 1
    
    def _rebuild_corridor_polygon(self):
        """从折线构建走廊多边形"""
        if len(self.corridor_polyline) < 2:
            self.survey_area_polygon = []
            return
        
        half_width = self.corridor_width / 2.0
        
        first_side_vertices = offset_polyline(self.corridor_polyline, half_width)
        second_side_vertices = offset_polyline(self.corridor_polyline, -half_width)
        
        # 通过组合两侧创建多边形
        self.survey_area_polygon = first_side_vertices.copy()
        for vertex in reversed(second_side_vertices):
            self.survey_area_polygon.append(vertex)
    
    def _has_turn_around(self):
        """检查是否启用转弯"""
        return self.turn_around_distance > 0
    
    def _rebuild_transects(self):
        """构建走廊的飞行条带"""
        self.transects = []
        
        transect_spacing = self._calc_transect_spacing()
        full_width = self.corridor_width
        half_width = full_width / 2.0
        transect_count = self._calc_transect_count()
        normalized_transect_position = transect_spacing / 2.0
        
        if len(self.corridor_polyline) < 2:
            return
        
        # 首先构建所有方向相同的飞行条带
        for i in range(transect_count):
            if transect_count == 1:
                # 单条飞行带飞过扫描线
                offset_distance = 0
            else:
                # 从归一化转换为绝对飞行条带偏移距离
                offset_distance = half_width - normalized_transect_position
            
            # 获取飞行条带坐标
            transect_coords = offset_polyline(self.corridor_polyline, offset_distance)
            
            # 创建带有坐标类型的飞行条带
            transect = []
            
            # 添加入口点
            transect.append({
                "coordinate": transect_coords[0],
                "coord_type": CoordType.SURVEY_ENTRY
            })
            
            # 添加内部点
            for j in range(1, len(transect_coords) - 1):
                transect.append({
                    "coordinate": transect_coords[j],
                    "coord_type": CoordType.INTERIOR
                })
            
            # 添加出口点
            transect.append({
                "coordinate": transect_coords[-1],
                "coord_type": CoordType.SURVEY_EXIT
            })
            
            # 如果需要，延长飞行条带端点用于转弯
            if self._has_turn_around():
                # 在开始处添加转弯
                azimuth = calculate_azimuth(transect_coords[0], transect_coords[1])
                turnaround_coord = calculate_coordinate_at_distance_and_azimuth(
                    transect_coords[0], -self.turn_around_distance, azimuth)
                
                transect.insert(0, {
                    "coordinate": turnaround_coord,
                    "coord_type": CoordType.TURNAROUND
                })
                
                # 在结束处添加转弯
                azimuth = calculate_azimuth(transect_coords[-1], transect_coords[-2])
                turnaround_coord = calculate_coordinate_at_distance_and_azimuth(
                    transect_coords[-1], -self.turn_around_distance, azimuth)
                
                transect.append({
                    "coordinate": turnaround_coord,
                    "coord_type": CoordType.TURNAROUND
                })
            
            self.transects.append(transect)
            normalized_transect_position += transect_spacing
        
        # 处理入口点调整
        reverse_transects = False
        reverse_vertices = False
        
        if self.entry_point == 0:
            reverse_transects = False
            reverse_vertices = False
        elif self.entry_point == 1:
            reverse_transects = True
            reverse_vertices = False
        elif self.entry_point == 2:
            reverse_transects = False
            reverse_vertices = True
        elif self.entry_point == 3:
            reverse_transects = True
            reverse_vertices = True
        
        if reverse_transects:
            self.transects.reverse()
        
        if reverse_vertices:
            for i in range(len(self.transects)):
                self.transects[i].reverse()
                
                # 交换入口和出口类型
                for coord in self.transects[i]:
                    if coord["coord_type"] == CoordType.SURVEY_ENTRY:
                        coord["coord_type"] = CoordType.SURVEY_EXIT
                    elif coord["coord_type"] == CoordType.SURVEY_EXIT:
                        coord["coord_type"] = CoordType.SURVEY_ENTRY
        
        # 调整为"割草机"模式
        reverse_vertices = False
        for i in range(len(self.transects)):
            if reverse_vertices:
                reverse_vertices = False
                
                # 反转此飞行条带
                self.transects[i].reverse()
                
                # 交换入口和出口类型
                for coord in self.transects[i]:
                    if coord["coord_type"] == CoordType.SURVEY_ENTRY:
                        coord["coord_type"] = CoordType.SURVEY_EXIT
                    elif coord["coord_type"] == CoordType.SURVEY_EXIT:
                        coord["coord_type"] = CoordType.SURVEY_ENTRY
            else:
                reverse_vertices = True
    
    def _recalc_complex_distance(self):
        """计算总飞行路径距离"""
        self.complex_distance = 0
        
        for transect in self.transects:
            prev_coord = None
            for coord_info in transect:
                if prev_coord is not None:
                    self.complex_distance += calculate_distance(
                        prev_coord["coordinate"], coord_info["coordinate"])
                prev_coord = coord_info
    
    def _recalc_camera_shots(self):
        """计算需要的相机拍摄次数"""
        trigger_distance = self.camera_calc.get("AdjustedFootprintFrontal", 0)
        
        if trigger_distance == 0:
            self.camera_shots = 0
        else:
            if self.camera_trigger_in_turn_around:
                self.camera_shots = math.ceil(self.complex_distance / trigger_distance)
            else:
                # 计算走廊折线长度
                corridor_length = 0
                for i in range(1, len(self.corridor_polyline)):
                    corridor_length += calculate_distance(
                        self.corridor_polyline[i-1], self.corridor_polyline[i])
                
                single_transect_image_count = math.ceil(corridor_length / trigger_distance)
                self.camera_shots = single_transect_image_count * self._calc_transect_count()
    
    def time_between_shots(self):
        """计算相机拍摄之间的时间（秒）"""
        if self.vehicle_speed == 0:
            return 0
        
        trigger_distance = self.camera_calc.get("AdjustedFootprintFrontal", 0)
        return trigger_distance / self.vehicle_speed
    
    def get_all_waypoints(self):
        """返回所有航点及其类型"""
        waypoints = []
        
        for transect_index, transect in enumerate(self.transects):
            for waypoint in transect:
                waypoint_info = {
                    "transect": transect_index,
                    "coordinate": waypoint["coordinate"],
                    "type": waypoint["coord_type"].name
                }
                waypoints.append(waypoint_info)
        
        return waypoints
        
    def append_waypoint(self, items, seq_num, coordinate, hold_time=0):
        """
        添加航点任务项
        
        参数:
            items: 任务项列表
            seq_num: 序列号
            coordinate: 坐标对象
            hold_time: 悬停时间（秒）
        
        返回:
            更新后的序列号
        """
        item = MissionItem(
            seq_num=seq_num,
            command=MavCmd.NAV_WAYPOINT,
            frame=MavFrame.GLOBAL_RELATIVE_ALT,
            param1=hold_time,  # 悬停时间
            param2=0.0,        # 接受半径（无）
            param3=0.0,        # 通过航点
            param4=None, # 偏航角（保持不变）
            latitude=coordinate.latitude,
            longitude=coordinate.longitude,
            altitude=coordinate.altitude,
            auto_continue=True,
            is_current=False
        )
        items.append(item)
        return seq_num + 1

    def append_camera_trigger_distance(self, items, seq_num, trigger_distance):
        """
        添加相机触发距离任务项
        
        参数:
            items: 任务项列表
            seq_num: 序列号
            trigger_distance: 触发距离（米），0表示停止触发
        
        返回:
            更新后的序列号
        """
        item = MissionItem(
            seq_num=seq_num,
            command=MavCmd.DO_SET_CAM_TRIGG_DIST,
            frame=MavFrame.MISSION,
            param1=trigger_distance,
            param2=0,          # 快门集成（忽略）
            param3=1,          # 立即触发一张照片
            param4=0,          # 未使用
            latitude=0,
            longitude=0,
            altitude=0,
            auto_continue=True,
            is_current=False
        )
        items.append(item)
        return seq_num + 1

    def append_camera_trigger_update(self, items, seq_num, coordinate, trigger_distance):
        """
        添加相机触发距离更新点（包括航点和触发命令）
        
        参数:
            items: 任务项列表
            seq_num: 序列号
            coordinate: 坐标对象
            trigger_distance: 触发距离（米）
        
        返回:
            更新后的序列号
        """
        # 先添加航点
        seq_num = self.append_waypoint(items, seq_num, coordinate)
        # 再添加触发距离命令
        seq_num = self.append_camera_trigger_distance(items, seq_num, trigger_distance)
        return seq_num

    def build_mission_items(self, start_seq_num=2):
        """
        构建任务项列表
        
        参数:
            start_seq_num: 起始序列号（doJumpId），默认从2开始
            
        返回:
            任务项列表, 视觉航线点列表
        """
        items = []
        seq_num = start_seq_num
        visual_transect_points = []
        
        # 准备坐标信息列表
        flight_path_coord_info = []
        
        # 将transects中的坐标信息转换为Coordinate对象并添加到列表
        for transect in self.transects:
            for point in transect:
                coord = point["coordinate"]
                if len(coord) > 2:
                    coordinate = Coordinate(
                        latitude=coord[0],
                        longitude=coord[1],
                        altitude=coord[2]
                    )
                else:
                    # 如果没有高度信息，使用相机的飞行高度
                    coordinate = Coordinate(
                        latitude=coord[0],
                        longitude=coord[1],
                        altitude=self.camera_calc.get("DistanceToSurface", 50.0)
                    )
                
                flight_path_coord_info.append({
                    "coord": coordinate,
                    "coord_type": point["coord_type"]
                })
                
                # 添加到视觉航线点列表（不包括转弯点）
                # if point["coord_type"] != CoordType.TURNAROUND:
                visual_transect_points.append([coordinate.latitude, coordinate.longitude])
        
        # 遍历所有飞行路径坐标
        for coord_index, coord_info in enumerate(flight_path_coord_info):
            # 检查是否为最后一个出口点
            last_survey_exit = coord_index == len(flight_path_coord_info) - 1
            
            # 根据坐标类型处理
            if coord_info["coord_type"] == CoordType.INTERIOR:
                # 内部普通点 - 只添加航点
                seq_num = self.append_waypoint(items, seq_num, coord_info["coord"])
                
            elif coord_info["coord_type"] == CoordType.TURNAROUND:
                # 转弯点 - 只添加航点
                seq_num = self.append_waypoint(items, seq_num, coord_info["coord"])
                
            elif coord_info["coord_type"] == CoordType.SURVEY_ENTRY:
                # 调查入口点
                if self.trigger_camera_enabled:
                    # 距离触发模式 - 添加航点和触发距离命令（开始拍照）
                    seq_num = self.append_waypoint(items, seq_num, coord_info["coord"])
                    seq_num = self.append_camera_trigger_distance(items, seq_num, self.trigger_distance_value)
                else:
                    # 不触发相机 - 只添加航点
                    seq_num = self.append_waypoint(items, seq_num, coord_info["coord"])
                    
            elif coord_info["coord_type"] == CoordType.SURVEY_EXIT:
                # 调查出口点
                if self.trigger_camera_enabled:
                    # 距离触发模式 - 添加航点和触发距离命令（停止拍照）
                    seq_num = self.append_waypoint(items, seq_num, coord_info["coord"])
                    seq_num = self.append_camera_trigger_distance(items, seq_num, 0)
                else:
                    # 不触发相机 - 只添加航点
                    seq_num = self.append_waypoint(items, seq_num, coord_info["coord"])
        
        return items, visual_transect_points
    
    def export_corridor_scan_item(self, start_seq_num=2):
        """
        导出走廊扫描任务项，仅包含走廊扫描相关信息
        
        参数:
            start_seq_num: 起始序列号（doJumpId），默认从1开始
            
        返回:
            走廊扫描任务项字典
        """
        # 构建任务项
        mission_items, visual_transect_points = self.build_mission_items(start_seq_num)
        
        # 创建CorridorScan复杂项
        corridor_scan_item = {
            "CorridorWidth": self.corridor_width,
            "EntryPoint": self.entry_point,
            "TransectStyleComplexItem": {
                "CameraCalc": self.camera_calc,
                "CameraShots": self.camera_shots,
                "CameraTriggerInTurnAround": self.camera_trigger_in_turn_around,
                "HoverAndCapture": self.hover_and_capture_enabled,
                "Items": [item.to_dict() for item in mission_items],
                "Refly90Degrees": False,
                "TurnAroundDistance": self.turn_around_distance,
                "VisualTransectPoints": visual_transect_points,
                "version": 2
            },
            "complexItemType": "CorridorScan",
            "polyline": [list(point[:2]) for point in self.corridor_polyline], # 只使用经纬度，不包含高度
            "type": "ComplexItem",
            "version": 2
        }
        
        return corridor_scan_item

def generate_corridor_scan_mission(polyline, camera_calc, corridor_width, start_seq_num=2):
    """
    创建走廊扫描任务项
    
    参数:
        polyline: 定义走廊中心线的[lat, lon]坐标列表
        camera_calc: 包含相机参数的字典
        corridor_width: 走廊宽度（米）
        start_seq_num: 起始序列号，默认为2
        
    返回:
        corridor_scan_item: 走廊扫描复杂任务项
        item_count: 任务项数量
    """
    # 将高度添加到折线
    altitude = camera_calc.get("DistanceToSurface", 50.0)
    polyline_with_alt = [[lat, lon, altitude] for lat, lon in polyline]
    
    # 创建走廊扫描项
    corridor_scan = CorridorScanComplexItem(
        corridor_polyline=polyline_with_alt,
        camera_calc=camera_calc,
        corridor_width=corridor_width
    )
    
    # 导出走廊扫描任务项
    corridor_scan_item = corridor_scan.export_corridor_scan_item(start_seq_num)
    
    # 获取任务项数量
    mission_items, _ = corridor_scan.build_mission_items(start_seq_num)
    item_count = len(mission_items)
    
    # 返回走廊扫描复杂任务项和数量
    return corridor_scan_item, item_count





if __name__ == "__main__":
    # 示例折线
    polyline = [
                    [
                        42.29593994763137,
                        -82.66273314523711
                    ],
                    [
                        42.29547951607164,
                        -82.66250132781317
                    ],
                    [
                        42.29541283260068,
                        -82.66158693682395
                    ],
                    [
                        42.295863738489906,
                        -82.66090865618561
                    ]
                ]
    
    # 相机参数
    camera_calc = {
                        "AdjustedFootprintFrontal": 10.895999999999999,
                        "AdjustedFootprintSide": 32.736,
                        "CameraName": "Sony ILCE-QX1",
                        "DistanceMode": 1,
                        "DistanceToSurface": 37.62758620689655,
                        "FixedOrientation": False,
                        "FocalLength": 16,
                        "FrontalOverlap": 70,
                        "ImageDensity": 1,
                        "ImageHeight": 3632,
                        "ImageWidth": 5456,
                        "Landscape": True,
                        "MinTriggerInterval": 0,
                        "SensorHeight": 15.4,
                        "SensorWidth": 23.2,
                        "SideOverlap": 40,
                        "ValueSetIsDistance": False,
                        "version": 2
                    }
    
    # 走廊宽度
    corridor_width = 50  # 米
    
    # 创建走廊扫描任务项
    corridor_scan_item, item_count = generate_corridor_scan_mission(polyline,camera_calc,corridor_width,start_seq_num=2)

    print(f"任务项数量：{item_count}")
    print(json.dumps(corridor_scan_item, indent=4))