<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实验详情 - 无人机任务规划实验系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .task-card {
            transition: transform 0.3s;
            margin-bottom: 1.5rem;
        }
        .task-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .badge-feasible {
            background-color: #198754;
        }
        .badge-infeasible {
            background-color: #dc3545;
        }
        .badge-partial {
            background-color: #fd7e14;
        }
        .task-type-badge {
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }
        .subtask-list {
            max-height: 300px;
            overflow-y: auto;
        }
        .form-floating {
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">无人机任务规划实验系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/experiments">实验管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/analysis">数据分析</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/settings">系统设置</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container py-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>环境 {{ env_id }} 详情</h1>
            <a href="/experiments" class="btn btn-outline-primary">
                <i class="bi bi-arrow-left"></i> 返回实验列表
            </a>
        </div>

        <!-- 环境信息 -->
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">环境信息</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>环境ID:</strong> {{ env_id }}</p>
                        <p><strong>文件路径:</strong> {{ experiment_data.file_path }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>处理时间:</strong> {{ experiment_data.processing_time|default('未处理', true) }} 秒</p>
                        <p><strong>任务数量:</strong> {{ experiment_data.user_requirements|length }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务列表 -->
        <h2 class="mb-4">任务列表</h2>

        {% if experiment_data.user_requirements %}
            {% for requirement in experiment_data.user_requirements %}
                {% set task_id = requirement.task_id %}
                {% set task_data = experiment_data.tasks.get(task_id, {}) %}

                <div class="card task-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">任务 {{ task_id }}</h5>

                        {% if task_data.analysis_result %}
                            {% set subtasks = task_data.analysis_result.subtasks|default([]) %}
                            {% set feasible_count = 0 %}
                            {% set total_subtasks = 0 %}

                            {% for subtask in subtasks %}
                                {% set total_subtasks = total_subtasks + 1 %}
                                {% if subtask.feasible is defined and subtask.feasible %}
                                    {% set feasible_count = feasible_count + 1 %}
                                {% endif %}
                            {% endfor %}

                            {% if total_subtasks > 0 %}
                                {% if feasible_count == 0 %}
                                    <span class="badge badge-infeasible text-white">不可行</span>
                                {% elif feasible_count == total_subtasks %}
                                    <span class="badge badge-feasible text-white">完全可行</span>
                                {% else %}
                                    <span class="badge badge-partial text-white">部分可行 ({{ feasible_count }}/{{ total_subtasks }})</span>
                                {% endif %}
                            {% else %}
                                {% if task_data.analysis_result.feasibility_analysis is defined and task_data.analysis_result.feasibility_analysis %}
                                    {% if "不可行" in task_data.analysis_result.feasibility_analysis %}
                                        <span class="badge badge-infeasible text-white">不可行</span>
                                    {% elif "可行" in task_data.analysis_result.feasibility_analysis %}
                                        <span class="badge badge-feasible text-white">可行</span>
                                    {% else %}
                                        <span class="badge bg-secondary text-white">未知</span>
                                    {% endif %}
                                {% else %}
                                    <span class="badge bg-secondary text-white">未知</span>
                                {% endif %}
                            {% endif %}
                        {% endif %}
                    </div>

                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6>任务描述:</h6>
                                    <div>
                                        <button type="button" class="btn btn-outline-primary btn-sm edit-task-btn" data-task-id="{{ task_id }}">
                                            <i class="bi bi-pencil"></i> 编辑
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm plan-task-btn" data-task-id="{{ task_id }}">
                                            <i class="bi bi-cpu"></i> 单独规划
                                        </button>
                                    </div>
                                </div>
                                <div id="taskDescription-{{ task_id }}">
                                    <p>{{ requirement.description }}</p>
                                    {% if requirement.description_cn %}
                                        <p class="text-muted">{{ requirement.description_cn }}</p>
                                    {% endif %}
                                </div>
                                <div id="taskDescriptionEdit-{{ task_id }}" style="display: none;">
                                    <form id="editForm-{{ task_id }}">
                                        <input type="hidden" name="env_id" value="{{ env_id }}">
                                        <input type="hidden" name="task_id" value="{{ task_id }}">
                                        <div class="mb-3">
                                            <label for="description-{{ task_id }}" class="form-label">英文描述</label>
                                            <textarea class="form-control" id="description-{{ task_id }}" name="description" rows="3">{{ requirement.description }}</textarea>
                                        </div>
                                        {% if requirement.description_cn %}
                                            <div class="mb-3">
                                                <label for="description_cn-{{ task_id }}" class="form-label">中文描述</label>
                                                <textarea class="form-control" id="description_cn-{{ task_id }}" name="description_cn" rows="3">{{ requirement.description_cn }}</textarea>
                                            </div>
                                        {% endif %}
                                        <div class="d-flex justify-content-end">
                                            <button type="button" class="btn btn-secondary btn-sm me-2 cancel-edit-btn" data-task-id="{{ task_id }}">取消</button>
                                            <button type="button" class="btn btn-primary btn-sm save-edit-btn" data-task-id="{{ task_id }}">保存</button>
                                        </div>
                                    </form>
                                </div>

                                {% if task_data.analysis_result %}
                                    <h6 class="mt-3">任务类型:</h6>
                                    <div>
                                        {% for task_type in task_data.analysis_result.task_types|default([]) %}
                                            <span class="badge bg-primary task-type-badge">{{ task_type }}</span>
                                        {% endfor %}
                                    </div>

                                    {% if task_data.analysis_result.task_summary %}
                                        <h6 class="mt-3">任务总结:</h6>
                                        <p>{{ task_data.analysis_result.task_summary }}</p>
                                    {% endif %}

                                    {% if task_data.analysis_result.subtasks and task_data.analysis_result.subtasks|length > 0 %}
                                        <h6 class="mt-3">子任务 ({{ task_data.analysis_result.subtasks|length }}):</h6>
                                        <div class="subtask-list">
                                            <ul class="list-group">
                                                {% for subtask in task_data.analysis_result.subtasks %}
                                                    <li class="list-group-item">
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <span>{{ subtask.subtask_id }}: {{ subtask.type }}</span>
                                                            {% if subtask.feasible is defined and subtask.feasible %}
                                                                <span class="badge bg-success">可行</span>
                                                            {% else %}
                                                                <span class="badge bg-danger">不可行</span>
                                                            {% endif %}
                                                        </div>
                                                        {% if subtask.target_area_name %}
                                                            <small class="text-muted">目标区域: {{ subtask.target_area_name }}</small>
                                                        {% endif %}
                                                    </li>
                                                {% endfor %}
                                            </ul>
                                        </div>
                                    {% endif %}

                                    <!-- 添加任务分析结果显示 -->
                                    <div class="mt-4">
                                        <button class="btn btn-outline-primary btn-sm show-analysis-btn" type="button" data-task-id="{{ task_id }}">
                                            显示完整分析结果
                                        </button>
                                    </div>
                                {% endif %}
                            </div>

                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">实验数据记录</h6>
                                    </div>
                                    <div class="card-body">
                                        <form id="recordForm-{{ task_id }}">
                                            <input type="hidden" name="env_id" value="{{ env_id }}">
                                            <input type="hidden" name="task_id" value="{{ task_id }}">

                                            <div class="form-floating">
                                                <input type="number" class="form-control" id="manualTime-{{ task_id }}" name="time_seconds" placeholder="0" min="0" step="0.1" value="{{ task_data.manual_planning_time|default('') }}">
                                                <label for="manualTime-{{ task_id }}">人工规划时间 (秒)</label>
                                            </div>

                                            <!-- 计时控制按钮 -->
                                            <div class="btn-group w-100 mb-2" role="group">
                                                <button type="button" class="btn btn-outline-success btn-sm start-timer-btn" data-task-id="{{ task_id }}">
                                                    <i class="bi bi-play-fill"></i> 开始
                                                </button>
                                                <button type="button" class="btn btn-outline-warning btn-sm pause-timer-btn" data-task-id="{{ task_id }}" disabled>
                                                    <i class="bi bi-pause-fill"></i> 暂停
                                                </button>
                                                <button type="button" class="btn btn-outline-danger btn-sm stop-timer-btn" data-task-id="{{ task_id }}" disabled>
                                                    <i class="bi bi-stop-fill"></i> 结束
                                                </button>
                                            </div>

                                            <!-- 计时显示 -->
                                            <div class="text-center mb-2 timer-display" id="timerDisplay-{{ task_id }}" style="display: none;">
                                                <span class="badge bg-secondary p-2">计时中: <span class="timer-value">00:00:00</span></span>
                                            </div>

                                            <button type="button" class="btn btn-primary btn-sm w-100 record-time-btn" data-task-id="{{ task_id }}">
                                                记录时间
                                            </button>

                                            <hr>

                                            <div class="form-floating">
                                                <select class="form-select" id="expertScore-{{ task_id }}" name="score">
                                                    <option value="" {% if not task_data.expert_score %}selected{% endif %}>选择评分</option>
                                                    {% for i in range(1, 11) %}
                                                        <option value="{{ i }}" {% if task_data.expert_score == i %}selected{% endif %}>{{ i }}</option>
                                                    {% endfor %}
                                                </select>
                                                <label for="expertScore-{{ task_id }}">专家评分 (1-10分)</label>
                                            </div>

                                            <button type="button" class="btn btn-success btn-sm w-100 record-score-btn" data-task-id="{{ task_id }}">
                                                记录评分
                                            </button>
                                        </form>

                                        <div class="mt-3">
                                            <p class="mb-1"><strong>LLM规划时间:</strong> {{ task_data.llm_planning_time|default('未记录', true) }} 秒</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        {% else %}
            <div class="alert alert-warning">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                未找到任何任务。请确保实验文件包含user_requirements字段。
            </div>
        {% endif %}
    </main>

    <!-- 结果提示 -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="resultToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <strong class="me-auto">操作结果</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body" id="toastMessage"></div>
        </div>
    </div>

    <!-- 任务分析结果悬浮窗口 -->
    <div class="modal fade" id="analysisModal" tabindex="-1" aria-labelledby="analysisModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="analysisModalLabel">任务分析结果</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <pre class="mb-0" style="max-height: 70vh; overflow-y: auto;"><code id="analysisContent"></code></pre>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 规划进度模态框 -->
    <div class="modal fade" id="planningModal" tabindex="-1" aria-labelledby="planningModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="planningModalLabel">任务规划中</h5>
                </div>
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">规划中...</span>
                    </div>
                    <p id="planningMessage">正在使用LLM规划任务，请稍候...</p>
                    <div class="progress mt-3">
                        <div id="planningProgress" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">© 2025 无人机任务规划实验系统 | 版权所有</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const resultToast = new bootstrap.Toast(document.getElementById('resultToast'));
            const toastMessage = document.getElementById('toastMessage');
            const analysisModal = new bootstrap.Modal(document.getElementById('analysisModal'));
            const analysisContent = document.getElementById('analysisContent');
            const analysisModalLabel = document.getElementById('analysisModalLabel');

            // 计时器相关变量
            const timers = {};

            // 格式化时间为 HH:MM:SS
            function formatTime(seconds) {
                const hours = Math.floor(seconds / 3600);
                const minutes = Math.floor((seconds % 3600) / 60);
                const secs = seconds % 60;

                return [
                    hours.toString().padStart(2, '0'),
                    minutes.toString().padStart(2, '0'),
                    secs.toString().padStart(2, '0')
                ].join(':');
            }

            // 开始计时
            document.querySelectorAll('.start-timer-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const taskId = this.getAttribute('data-task-id');
                    const timerDisplay = document.getElementById(`timerDisplay-${taskId}`);
                    const timerValue = timerDisplay.querySelector('.timer-value');
                    const pauseBtn = document.querySelector(`.pause-timer-btn[data-task-id="${taskId}"]`);
                    const stopBtn = document.querySelector(`.stop-timer-btn[data-task-id="${taskId}"]`);

                    // 如果已经有计时器在运行，先清除
                    if (timers[taskId] && timers[taskId].interval) {
                        clearInterval(timers[taskId].interval);
                    }

                    // 初始化计时器
                    if (!timers[taskId]) {
                        timers[taskId] = {
                            startTime: Date.now(),
                            pausedTime: 0,
                            isPaused: false,
                            totalSeconds: 0,
                            interval: null
                        };
                    } else if (timers[taskId].isPaused) {
                        // 如果是从暂停状态恢复，更新开始时间
                        timers[taskId].startTime = Date.now() - (timers[taskId].pausedTime * 1000);
                        timers[taskId].isPaused = false;
                    } else {
                        // 重新开始计时
                        timers[taskId].startTime = Date.now();
                        timers[taskId].pausedTime = 0;
                        timers[taskId].totalSeconds = 0;
                    }

                    // 显示计时器
                    timerDisplay.style.display = 'block';

                    // 启用暂停和停止按钮，禁用开始按钮
                    this.disabled = true;
                    pauseBtn.disabled = false;
                    stopBtn.disabled = false;

                    // 开始计时
                    timers[taskId].interval = setInterval(() => {
                        const elapsedSeconds = Math.floor((Date.now() - timers[taskId].startTime) / 1000);
                        timers[taskId].totalSeconds = elapsedSeconds;
                        timerValue.textContent = formatTime(elapsedSeconds);
                    }, 1000);
                });
            });

            // 暂停计时
            document.querySelectorAll('.pause-timer-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const taskId = this.getAttribute('data-task-id');
                    const startBtn = document.querySelector(`.start-timer-btn[data-task-id="${taskId}"]`);

                    if (timers[taskId] && timers[taskId].interval) {
                        // 暂停计时器
                        clearInterval(timers[taskId].interval);
                        timers[taskId].interval = null;
                        timers[taskId].isPaused = true;
                        timers[taskId].pausedTime = timers[taskId].totalSeconds;

                        // 启用开始按钮，禁用暂停按钮
                        startBtn.disabled = false;
                        this.disabled = true;
                    }
                });
            });

            // 停止计时
            document.querySelectorAll('.stop-timer-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const taskId = this.getAttribute('data-task-id');
                    const timerDisplay = document.getElementById(`timerDisplay-${taskId}`);
                    const startBtn = document.querySelector(`.start-timer-btn[data-task-id="${taskId}"]`);
                    const pauseBtn = document.querySelector(`.pause-timer-btn[data-task-id="${taskId}"]`);
                    const timeInput = document.getElementById(`manualTime-${taskId}`);

                    if (timers[taskId]) {
                        // 停止计时器
                        if (timers[taskId].interval) {
                            clearInterval(timers[taskId].interval);
                            timers[taskId].interval = null;
                        }

                        // 设置输入框的值
                        const totalSeconds = timers[taskId].isPaused ?
                            timers[taskId].pausedTime :
                            Math.floor((Date.now() - timers[taskId].startTime) / 1000);

                        timeInput.value = totalSeconds;

                        // 重置计时器
                        timers[taskId] = null;

                        // 隐藏计时器显示
                        timerDisplay.style.display = 'none';

                        // 重置按钮状态
                        startBtn.disabled = false;
                        pauseBtn.disabled = true;
                        this.disabled = true;
                    }
                });
            });

            // 记录人工规划时间
            document.querySelectorAll('.record-time-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const taskId = this.getAttribute('data-task-id');
                    const form = document.getElementById(`recordForm-${taskId}`);
                    const formData = new FormData(form);

                    fetch('/record_manual_time', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            toastMessage.textContent = data.message;
                            toastMessage.className = 'toast-body text-success';
                        } else {
                            toastMessage.textContent = data.message;
                            toastMessage.className = 'toast-body text-danger';
                        }
                        resultToast.show();
                    })
                    .catch(error => {
                        toastMessage.textContent = '操作失败: ' + error.message;
                        toastMessage.className = 'toast-body text-danger';
                        resultToast.show();
                    });
                });
            });

            // 记录专家评分
            document.querySelectorAll('.record-score-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const taskId = this.getAttribute('data-task-id');
                    const form = document.getElementById(`recordForm-${taskId}`);
                    const formData = new FormData(form);

                    fetch('/record_expert_score', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            toastMessage.textContent = data.message;
                            toastMessage.className = 'toast-body text-success';
                        } else {
                            toastMessage.textContent = data.message;
                            toastMessage.className = 'toast-body text-danger';
                        }
                        resultToast.show();
                    })
                    .catch(error => {
                        toastMessage.textContent = '操作失败: ' + error.message;
                        toastMessage.className = 'toast-body text-danger';
                        resultToast.show();
                    });
                });
            });

            // 显示任务分析结果
            document.querySelectorAll('.show-analysis-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const taskId = this.getAttribute('data-task-id');

                    // 获取任务数据
                    const taskData = {
                        {% for requirement in experiment_data.user_requirements %}
                            {% set req_task_id = requirement.task_id %}
                            {% set task_data = experiment_data.tasks.get(req_task_id, {}) %}
                            '{{ req_task_id }}': {
                                analysisResult: {{ task_data.analysis_result|tojson }}
                            }{% if not loop.last %},{% endif %}
                        {% endfor %}
                    };

                    if (taskData[taskId]) {
                        analysisModalLabel.textContent = `任务 ${taskId} 分析结果`;
                        analysisContent.textContent = JSON.stringify(taskData[taskId].analysisResult, null, 2);
                        analysisModal.show();
                    }
                });
            });

            // 点击模态框外部关闭
            document.getElementById('analysisModal').addEventListener('click', function(event) {
                if (event.target === this) {
                    analysisModal.hide();
                }
            });

            // 规划进度模态框
            const planningModal = new bootstrap.Modal(document.getElementById('planningModal'));
            const planningProgress = document.getElementById('planningProgress');
            const planningMessage = document.getElementById('planningMessage');

            // 编辑任务描述
            document.querySelectorAll('.edit-task-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const taskId = this.getAttribute('data-task-id');
                    const descriptionDiv = document.getElementById(`taskDescription-${taskId}`);
                    const editDiv = document.getElementById(`taskDescriptionEdit-${taskId}`);

                    // 显示编辑表单，隐藏描述
                    descriptionDiv.style.display = 'none';
                    editDiv.style.display = 'block';
                });
            });

            // 取消编辑
            document.querySelectorAll('.cancel-edit-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const taskId = this.getAttribute('data-task-id');
                    const descriptionDiv = document.getElementById(`taskDescription-${taskId}`);
                    const editDiv = document.getElementById(`taskDescriptionEdit-${taskId}`);

                    // 显示描述，隐藏编辑表单
                    descriptionDiv.style.display = 'block';
                    editDiv.style.display = 'none';
                });
            });

            // 保存编辑
            document.querySelectorAll('.save-edit-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const taskId = this.getAttribute('data-task-id');
                    const form = document.getElementById(`editForm-${taskId}`);
                    const formData = new FormData(form);

                    fetch('/update_task_description', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            toastMessage.textContent = data.message;
                            toastMessage.className = 'toast-body text-success';

                            // 更新页面上的描述
                            const descriptionDiv = document.getElementById(`taskDescription-${taskId}`);
                            const editDiv = document.getElementById(`taskDescriptionEdit-${taskId}`);

                            // 更新英文描述
                            const descriptionEn = form.querySelector(`#description-${taskId}`).value;
                            const descriptionCn = form.querySelector(`#description_cn-${taskId}`)?.value;

                            // 更新HTML
                            let html = `<p>${descriptionEn}</p>`;
                            if (descriptionCn) {
                                html += `<p class="text-muted">${descriptionCn}</p>`;
                            }
                            descriptionDiv.innerHTML = html;

                            // 显示描述，隐藏编辑表单
                            descriptionDiv.style.display = 'block';
                            editDiv.style.display = 'none';
                        } else {
                            toastMessage.textContent = data.message;
                            toastMessage.className = 'toast-body text-danger';
                        }
                        resultToast.show();
                    })
                    .catch(error => {
                        toastMessage.textContent = '操作失败: ' + error.message;
                        toastMessage.className = 'toast-body text-danger';
                        resultToast.show();
                    });
                });
            });

            // 单独规划任务
            document.querySelectorAll('.plan-task-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const taskId = this.getAttribute('data-task-id');

                    // 显示规划进度模态框
                    planningProgress.style.width = '0%';
                    planningMessage.textContent = '正在使用LLM规划任务，请稍候...';
                    planningModal.show();

                    // 模拟进度更新
                    let progress = 0;
                    const progressInterval = setInterval(() => {
                        progress += 5;
                        if (progress > 90) {
                            clearInterval(progressInterval);
                        }
                        planningProgress.style.width = `${progress}%`;
                    }, 1000);

                    // 发送规划请求
                    fetch(`/plan_single_task/${env_id}/${taskId}`, {
                        method: 'POST'
                    })
                    .then(response => response.json())
                    .then(data => {
                        // 清除进度更新
                        clearInterval(progressInterval);
                        planningProgress.style.width = '100%';

                        if (data.success) {
                            planningMessage.textContent = '规划完成！正在刷新页面...';

                            // 延迟关闭模态框并刷新页面
                            setTimeout(() => {
                                planningModal.hide();
                                window.location.reload();
                            }, 1500);
                        } else {
                            planningMessage.textContent = `规划失败: ${data.message}`;

                            // 延迟关闭模态框
                            setTimeout(() => {
                                planningModal.hide();

                                // 显示错误消息
                                toastMessage.textContent = data.message;
                                toastMessage.className = 'toast-body text-danger';
                                resultToast.show();
                            }, 1500);
                        }
                    })
                    .catch(error => {
                        // 清除进度更新
                        clearInterval(progressInterval);

                        planningMessage.textContent = `规划失败: ${error.message}`;

                        // 延迟关闭模态框
                        setTimeout(() => {
                            planningModal.hide();

                            // 显示错误消息
                            toastMessage.textContent = '操作失败: ' + error.message;
                            toastMessage.className = 'toast-body text-danger';
                            resultToast.show();
                        }, 1500);
                    });
                });
            });
        });
    </script>
</body>
</html>
