/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 100%;
    margin: 0 auto;
    padding: 0;
    display: flex;
    flex-direction: column;
    height: 100vh;
}

header {
    background-color: #2c3e50;
    color: white;
    padding: 1rem;
    text-align: center;
}

footer {
    background-color: #2c3e50;
    color: white;
    padding: 0.5rem;
    text-align: center;
    font-size: 0.8rem;
}

/* 地图容器样式 */
.map-container {
    display: flex;
    flex: 1;
    overflow: hidden;
}

#map {
    flex: 1;
    height: 100%;
    z-index: 1;
}

.sidebar {
    width: 300px;
    background-color: white;
    box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

/* 控制面板样式 */
.control-panel {
    padding: 1rem;
    flex: 1;
}

.panel-section {
    margin-bottom: 1.5rem;
    border-bottom: 1px solid #eee;
    padding-bottom: 1rem;
}

.panel-section:last-child {
    border-bottom: none;
}

h3 {
    margin-bottom: 1rem;
    color: #2c3e50;
    font-size: 1.2rem;
}

h4 {
    margin-bottom: 0.5rem;
    color: #34495e;
    font-size: 1rem;
}

/* 按钮样式 */
.btn {
    padding: 0.5rem 1rem;
    background-color: #ecf0f1;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: #dde4e6;
}

.btn.primary {
    background-color: #3498db;
    color: white;
}

.btn.primary:hover {
    background-color: #2980b9;
}

/* 信息框样式 */
.info-box, .result-box {
    background-color: #f9f9f9;
    border: 1px solid #eee;
    border-radius: 4px;
    padding: 0.5rem;
    margin-top: 0.5rem;
    font-size: 0.9rem;
}

/* 图例样式 */
.legend {
    padding: 1rem;
    background-color: #f9f9f9;
    border-top: 1px solid #eee;
}

.legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.legend-color {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 0.5rem;
    border-radius: 3px;
    border: 1px solid rgba(0, 0, 0, 0.2);
}

/* 图层控制样式 */
#layer-controls {
    display: flex;
    flex-direction: column;
}

.layer-control {
    display: flex;
    align-items: center;
    margin-bottom: 0.3rem;
}

.layer-control input {
    margin-right: 0.5rem;
}

/* 坐标编辑表单 */
.coordinate-form {
    margin-top: 0.5rem;
}

.coordinate-form label {
    display: block;
    margin-bottom: 0.2rem;
    font-size: 0.8rem;
    color: #666;
}

.coordinate-form input {
    width: 100%;
    padding: 0.3rem;
    margin-bottom: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 3px;
}

/* 高亮样式 */
.highlighted-point {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.2);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .map-container {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        height: 300px;
    }
}
