import math
import json
from enum import Enum
from typing import List, Dict, Any, Tuple, Optional
import numpy as np

class MavFrame(Enum):
    GLOBAL_RELATIVE_ALT = 3
    MISSION = 2

class MavCmd(Enum):
    NAV_WAYPOINT = 16
    DO_SET_ROI_WPNEXT_OFFSET = 195
    DO_SET_CAM_TRIGG_DIST = 206
    DO_SET_ROI_NONE = 197
    
class MissionItem:
    def __init__(self, seq_num, command, frame, param1, param2, param3, param4, x, y, z, 
                 autoContinue=True, isCurrentItem=False):
        self.seq_num = seq_num
        self.command = command
        self.frame = frame
        self.param1 = param1
        self.param2 = param2
        self.param3 = param3
        self.param4 = param4
        self.x = x
        self.y = y
        self.z = z
        self.autoContinue = autoContinue
        self.isCurrentItem = isCurrentItem
        
    def to_dict(self):
        return {
            "seqNum": self.seq_num,
            "command": self.command.value,
            "frame": self.frame.value,
            "param1": self.param1,
            "param2": self.param2,
            "param3": self.param3,
            "param4": self.param4,
            "x": self.x,
            "y": self.y,
            "z": self.z,
            "autoContinue": self.autoContinue,
            "isCurrentItem": self.isCurrentItem
        }

class GeoCoordinate:
    def __init__(self, latitude, longitude, altitude=0):
        self.latitude = latitude
        self.longitude = longitude
        self.altitude = altitude
        
    def distance_to(self, other):
        """Calculate distance between two coordinates in meters"""
        # Use Haversine formula
        R = 6371000  # Earth radius in meters
        phi1 = math.radians(self.latitude)
        phi2 = math.radians(other.latitude)
        delta_phi = math.radians(other.latitude - self.latitude)
        delta_lambda = math.radians(other.longitude - self.longitude)
        
        a = math.sin(delta_phi/2) * math.sin(delta_phi/2) + \
            math.cos(phi1) * math.cos(phi2) * \
            math.sin(delta_lambda/2) * math.sin(delta_lambda/2)
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        
        return R * c
    
    def __str__(self):
        return f"({self.latitude}, {self.longitude}, {self.altitude})"

class CameraCalc:
    def __init__(self, camera_params):
        self.params = camera_params
        
    def adjusted_footprint_frontal(self):
        return self.params.get("AdjustedFootprintFrontal", 0)
        
    def adjusted_footprint_side(self):
        return self.params.get("AdjustedFootprintSide", 0)
        
    def distance_to_surface(self):
        return self.params.get("DistanceToSurface", 0)
        
    def to_dict(self):
        return self.params

class MapPolygon:
    def __init__(self, coordinates=None):
        self.coordinates = coordinates or []
        
    def add_vertex(self, coord):
        self.coordinates.append(coord)
        
    def vertex_coordinate(self, index):
        if index < 0 or index >= len(self.coordinates):
            return None
        return self.coordinates[index]
        
    def count(self):
        return len(self.coordinates)
        
    def coordinate_list(self):
        return self.coordinates
        
    def is_valid(self):
        return len(self.coordinates) >= 3
    
    def offset(self, distance_meters):
        """Create an offset polygon at specified distance around the original"""
        if len(self.coordinates) < 3:
            return MapPolygon(self.coordinates.copy())
        
        # Convert coordinates to numpy arrays for easier math
        points = np.array([[c.latitude, c.longitude] for c in self.coordinates])
        
        # Calculate the centroid
        centroid = np.mean(points, axis=0)
        
        # For each point:
        # 1. Get vector from centroid to point
        # 2. Normalize vector
        # 3. Scale by distance (with approximate conversion to degrees)
        
        # ~111,000 meters per degree of latitude
        # ~111,000 * cos(lat) meters per degree of longitude
        lat_meters_per_degree = 111000
        
        offset_coords = []
        for coord in self.coordinates:
            # Vector from centroid to point
            dir_lat = coord.latitude - centroid[0]
            dir_lon = coord.longitude - centroid[1]
            
            # Calculate distance from centroid to point (in degrees)
            dist = math.sqrt(dir_lat**2 + dir_lon**2)
            
            if dist > 0:
                # Normalize vector
                dir_lat /= dist
                dir_lon /= dist
                
                # Convert distance from meters to approximate degrees
                lat_offset_deg = distance_meters / lat_meters_per_degree
                lon_offset_deg = distance_meters / (lat_meters_per_degree * 
                                                    math.cos(math.radians(coord.latitude)))
                
                # Scale and add to original coordinates
                offset_lat = coord.latitude + dir_lat * lat_offset_deg
                offset_lon = coord.longitude + dir_lon * lon_offset_deg
                
                offset_coords.append(GeoCoordinate(offset_lat, offset_lon))
            else:
                offset_coords.append(GeoCoordinate(coord.latitude, coord.longitude))
        
        return MapPolygon(offset_coords)

class StructureScanComplexItem:
    def __init__(self, camera_params, structure_params, home_altitude=0):
        # Camera parameters
        self.camera_calc = CameraCalc(camera_params)
        
        # Get structure height first, as other parameters may depend on it
        self.structure_height = structure_params.get("StructureHeight", 0)
        
        # Structure parameters with defaults based on structure height
        self.entrance_alt = structure_params.get("EntranceAltitude", self.structure_height)
        self.scan_bottom_alt = structure_params.get("ScanBottomAlt", self.structure_height / 2)
        self.gimbal_pitch = structure_params.get("GimbalPitch", 45)
        self.start_from_top = structure_params.get("StartFromTop", True)
        self.layers = 1  # Will be calculated in _recalc_layer_info
        
        # Initialize polygons
        self.structure_polygon = MapPolygon()
        self.flight_polygon = MapPolygon()
        
        # Other parameters
        self.entry_vertex = 0
        self.home_altitude = home_altitude
        self.sequence_number = 0
        self.camera_shots = 0
        self.scan_distance = 0
        
        # Load structure polygon if provided
        if "polygon" in structure_params:
            for coord in structure_params["polygon"]:
                self.structure_polygon.add_vertex(GeoCoordinate(coord[0], coord[1]))
                
        # Calculate flight polygon (offset from structure)
        self._rebuild_flight_polygon()
        
        # Calculate other mission parameters
        self._recalc_layer_info()
        self._recalc_camera_shots()
        self._recalc_scan_distance()
    
    def _rebuild_flight_polygon(self):
        """Rebuild the flight polygon based on the structure polygon and offset distance"""
        distance = self.camera_calc.distance_to_surface()
        self.flight_polygon = self.structure_polygon.offset(distance)
    
    def _recalc_layer_info(self):
        """Recalculate layer information based on structure height and camera parameters"""
        # Calculate usable height (from bottom altitude to structure height)
        surface_height = max(self.structure_height - self.scan_bottom_alt, 0)
        footprint_frontal = self.camera_calc.adjusted_footprint_frontal()
        
        if footprint_frontal <= 0:
            self.layers = 1
        else:
            # Calculate number of layers needed to cover the structure
            # This matches the original C++ code's calculation method
            self.layers = max(math.ceil(surface_height / footprint_frontal), 1)
    
    def _recalc_camera_shots(self):
        """Calculate the number of camera shots needed for the mission"""
        trigger_distance = self.camera_calc.adjusted_footprint_side()
        if trigger_distance <= 0 or self.flight_polygon.count() < 3:
            self.camera_shots = 0
            return
        
        # Calculate perimeter distance
        coords = self.flight_polygon.coordinate_list()
        distance = 0
        for i in range(len(coords)):
            coord1 = coords[i]
            coord2 = coords[(i + 1) % len(coords)]
            distance += coord1.distance_to(coord2)
        
        if distance <= 0:
            self.camera_shots = 0
            return
        
        # Calculate shots per layer and total shots
        shots_per_layer = int(distance / trigger_distance)
        self.camera_shots = shots_per_layer * self.layers
    
    def _recalc_scan_distance(self):
        """Calculate the total scan distance"""
        if self.flight_polygon.count() < 3:
            self.scan_distance = 0
            return
            
        coords = self.flight_polygon.coordinate_list()
        distance = 0
        for i in range(len(coords)):
            coord1 = coords[i]
            coord2 = coords[(i + 1) % len(coords)]
            distance += coord1.distance_to(coord2)
        
        # Multiply by number of layers
        distance *= self.layers
        
        # Add vertical component
        surface_height = max(self.structure_height - self.scan_bottom_alt, 0)
        distance += surface_height
        
        self.scan_distance = distance
    
    def bottom_flight_alt(self):
        """Calculate the bottom flight altitude"""
        footprint_frontal = self.camera_calc.adjusted_footprint_frontal()
        
        if self.start_from_top:
            # Structure Height minus the topmost layers
            layer_increment = (footprint_frontal / 2.0) + ((self.layers - 1) * footprint_frontal)
            return self.structure_height - layer_increment
        else:
            # Bottom alt plus half the height of a layer
            layer_increment = footprint_frontal / 2.0
            return self.scan_bottom_alt + layer_increment
    
    def top_flight_alt(self):
        """Calculate the top flight altitude"""
        footprint_frontal = self.camera_calc.adjusted_footprint_frontal()
        
        if self.start_from_top:
            # Structure Height minus half the layer height
            layer_increment = footprint_frontal / 2.0
            return self.structure_height - layer_increment
        else:
            # Bottom alt plus all layers
            layer_increment = (footprint_frontal / 2.0) + ((self.layers - 1) * footprint_frontal)
            return self.scan_bottom_alt + layer_increment
    
    def count_sequence_number(self):
        """Calculate the last sequence number for the mission"""
        # Each layer contains:
        # - 1 waypoint for each polygon vertex + 1 to return to first vertex
        # - 2 commands for camera trigger start/stop
        layer_item_count = self.flight_polygon.count() + 1 + 2
        
        # Multiply by number of layers
        multi_layer_item_count = layer_item_count * self.layers
        
        # Add:
        # - 2 for ROI commands (point at structure and reset)
        # - 2 for entrance/exit waypoints
        item_count = multi_layer_item_count + 2 + 2
        
        return self.sequence_number + item_count
    
    def generate_mission_items(self, seq_num=0):
        """Generate mission items for the structure scan"""
        items = []
        self.sequence_number = seq_num
        seqNum = seq_num
        
        start_from_top = self.start_from_top
        start_altitude = self.structure_height if start_from_top else self.scan_bottom_alt
        
        # Entrance waypoint
        entrance_exit_coord = self.flight_polygon.vertex_coordinate(self.entry_vertex)
        if entrance_exit_coord is None:
            return items  # No valid entrance point
            
        items.append(MissionItem(
            seqNum,
            MavCmd.NAV_WAYPOINT,
            MavFrame.GLOBAL_RELATIVE_ALT,
            0,  # No hold time
            0.0,  # No acceptance radius
            0.0,  # Pass through waypoint
            float('nan'),  # Yaw unchanged
            entrance_exit_coord.latitude,
            entrance_exit_coord.longitude,
            self.entrance_alt,
            True,  # autoContinue
            False  # isCurrentItem
        ))
        seqNum += 1
        
        # Point camera at structure
        items.append(MissionItem(
            seqNum,
            MavCmd.DO_SET_ROI_WPNEXT_OFFSET,
            MavFrame.MISSION,
            0, 0, 0, 0,  # param 1-4 not used
            self.gimbal_pitch,
            0,  # Roll stays in standard orientation
            90,  # 90 degree yaw offset to point to structure
            True,  # autoContinue
            False  # isCurrentItem
        ))
        seqNum += 1
        
        # Set up for the first layer
        layer_altitude = start_altitude
        half_layer_height = self.camera_calc.adjusted_footprint_frontal() / 2.0
        if start_from_top:
            layer_altitude -= half_layer_height
        else:
            layer_altitude += half_layer_height
        
        # Process each layer
        for layer in range(self.layers):
            add_trigger_start = True
            
            done = False
            current_vertex = self.entry_vertex
            processed_vertices = 0
            
            while not done:
                vertex_coord = self.flight_polygon.vertex_coordinate(current_vertex)
                if vertex_coord is None:
                    break
                    
                # Add waypoint
                items.append(MissionItem(
                    seqNum,
                    MavCmd.NAV_WAYPOINT,
                    MavFrame.GLOBAL_RELATIVE_ALT,
                    0,  # No hold time
                    0.0,  # No acceptance radius
                    0.0,  # Pass through waypoint
                    float('nan'),  # Yaw unchanged
                    vertex_coord.latitude,
                    vertex_coord.longitude,
                    layer_altitude,
                    True,  # autoContinue
                    False  # isCurrentItem
                ))
                seqNum += 1
                
                # Start camera triggering after first waypoint in layer
                if add_trigger_start:
                    add_trigger_start = False
                    items.append(MissionItem(
                        seqNum,
                        MavCmd.DO_SET_CAM_TRIGG_DIST,
                        MavFrame.MISSION,
                        self.camera_calc.adjusted_footprint_side(),  # trigger distance
                        0,  # shutter integration (ignore)
                        1,  # trigger immediately when starting
                        0, 0, 0, 0,  # param 4-7 unused
                        True,  # autoContinue
                        False  # isCurrentItem
                    ))
                    seqNum += 1
                
                # Move to next vertex
                current_vertex += 1
                if current_vertex >= self.flight_polygon.count():
                    current_vertex = 0
                
                # Check if we've processed all vertices
                processed_vertices += 1
                done = processed_vertices == self.flight_polygon.count() + 1
            
            # Stop camera triggering after last waypoint in layer
            items.append(MissionItem(
                seqNum,
                MavCmd.DO_SET_CAM_TRIGG_DIST,
                MavFrame.MISSION,
                0,  # stop triggering
                0,  # shutter integration (ignore)
                0,  # trigger immediately when starting
                0, 0, 0, 0,  # param 4-7 unused
                True,  # autoContinue
                False  # isCurrentItem
            ))
            seqNum += 1
            
            # Move to next layer altitude
            if start_from_top:
                layer_altitude -= half_layer_height * 2
            else:
                layer_altitude += half_layer_height * 2
        
        # Return camera to neutral position
        items.append(MissionItem(
            seqNum,
            MavCmd.DO_SET_ROI_NONE,
            MavFrame.MISSION,
            0, 0, 0, 0, 0, 0, 0,  # param 1-7 not used
            True,  # autoContinue
            False  # isCurrentItem
        ))
        seqNum += 1
        
        # Exit waypoint
        items.append(MissionItem(
            seqNum,
            MavCmd.NAV_WAYPOINT,
            MavFrame.GLOBAL_RELATIVE_ALT,
            0,  # No hold time
            0.0,  # No acceptance radius
            0.0,  # Pass through waypoint
            float('nan'),  # Yaw unchanged
            entrance_exit_coord.latitude,
            entrance_exit_coord.longitude,
            self.entrance_alt,
            True,  # autoContinue
            False  # isCurrentItem
        ))
        seqNum += 1
        
        return items
    
    def export_mission_item(self):
        """Export the structure scan as a mission item JSON"""
        return {
            "CameraCalc": self.camera_calc.to_dict(),
            "EntranceAltitude": self.entrance_alt,
            "GimbalPitch": self.gimbal_pitch,
            "Layers": self.layers,  # Now calculated, not from input
            "ScanBottomAlt": self.scan_bottom_alt,
            "StartFromTop": self.start_from_top,
            "StructureHeight": self.structure_height,
            "complexItemType": "StructureScan",
            "polygon": [[coord.latitude, coord.longitude] for coord in self.structure_polygon.coordinate_list()],
            "type": "ComplexItem",
            "version": 3
        }
    
    def get_mission_stats(self):
        """Return summary statistics for the mission"""
        return {
            "cameraShots": self.camera_shots,
            "totalLayers": self.layers,
            "topFlightAlt": self.top_flight_alt(),
            "bottomFlightAlt": self.bottom_flight_alt(),
            "scanDistance": self.scan_distance,
            "totalSequenceNumber": self.count_sequence_number(),
            "polygonVertexCount": self.structure_polygon.count(),
            "flightPolygonVertexCount": self.flight_polygon.count()
        }

# 添加的新函数，用于简化结构扫描任务的创建
def generate_structure_scan_mission(polygon, camera_calc, structure_height):
    """
    创建结构扫描任务并返回任务项和总数量
    
    参数:
        polygon (list): 结构多边形坐标列表，格式为 [[lat1, lon1], [lat2, lon2], ...]
        camera_calc (dict): 相机参数字典
        structure_height (float): 结构高度（米）
        
    返回:
        tuple: (structure_scan_item, total_mission_items)
            - structure_scan_item: 结构扫描对象
            - total_mission_items: 任务项总数量
    """
    # 创建结构参数字典
    structure_params = {
        "StructureHeight": structure_height,
        "polygon": polygon
    }
    
    # 创建结构扫描对象
    structure_scan = StructureScanComplexItem(camera_calc, structure_params)
    
    # 生成任务项
    mission_items = structure_scan.generate_mission_items()

    structure_scan_item = structure_scan.export_mission_item()
    
    # 获取任务项总数
    item_count = len(mission_items)
    
    return structure_scan_item, item_count

if __name__ == "__main__":
        # 示例使用
    polygon = [
                    [
                        42.295608783721654,
                        -82.66209663613478
                    ],
                    [
                        42.29553132691014,
                        -82.6621018719372
                    ],
                    [
                        42.2955371361739,
                        -82.66234795505868
                    ],
                    [
                        42.29561459297826,
                        -82.66234271925626
                    ]
                ]
    
    camera_calc = {
                    "AdjustedFootprintFrontal": 10.895999999999999,
                    "AdjustedFootprintSide": 21.824,
                    "CameraName": "Sony ILCE-QX1",
                    "DistanceMode": 1,
                    "DistanceToSurface": 37.62758620689655,
                    "FixedOrientation": False,
                    "FocalLength": 16,
                    "FrontalOverlap": 70,
                    "ImageDensity": 1,
                    "ImageHeight": 3632,
                    "ImageWidth": 5456,
                    "Landscape": True,
                    "MinTriggerInterval": 0,
                    "SensorHeight": 15.4,
                    "SensorWidth": 23.2,
                    "SideOverlap": 60,
                    "ValueSetIsDistance": False,
                    "version": 2
                }
    
    structure_height = 50
    
    structure_scan_item, item_count = generate_structure_scan_mission(polygon, camera_calc, structure_height)
    
    print(f"任务项数量: {item_count}")
    print(json.dumps(structure_scan_item, indent=4))