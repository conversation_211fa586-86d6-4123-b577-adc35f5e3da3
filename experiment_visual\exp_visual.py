#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
实验数据处理和记录系统的可视化界面
使用Flask框架创建Web应用
"""

import os
import sys
import json
import time
import logging
from datetime import datetime
from flask import Flask, render_template, request, jsonify, redirect, url_for, send_file
import pandas as pd
import plotly
import plotly.express as px
import plotly.graph_objects as go
from werkzeug.utils import secure_filename

# 导入实验管理器
from experiment_manager import ExperimentManager
from experiment_processor import ExperimentProcessor
from experiment_recorder import ExperimentRecorder

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ExperimentApp")

# 创建Flask应用
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key'
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB限制

# 确保上传目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# 创建实验管理器实例
experiment_manager = ExperimentManager()

def get_available_experiment_records():
    """获取所有可用的实验记录文件"""
    records = []
    experiment_results_dir = "experiment_results"
    if os.path.exists(experiment_results_dir):
        for file in os.listdir(experiment_results_dir):
            if file.startswith("experiment_record_") and file.endswith(".json"):
                timestamp = file.replace("experiment_record_", "").replace(".json", "")
                records.append({
                    "file_name": file,
                    "timestamp": timestamp,
                    "path": os.path.join(experiment_results_dir, file),
                    "display_name": f"实验记录 ({timestamp})"
                })
    # 按时间戳降序排序（最新的在前面）
    records.sort(key=lambda x: x["timestamp"], reverse=True)
    return records

@app.route('/')
def index():
    """主页"""
    # 获取所有可用的实验记录
    records = get_available_experiment_records()
    return render_template('index.html', records=records)

@app.route('/select_record', methods=['POST'])
def select_record():
    """选择实验记录"""
    record_path = request.form.get('record_path')

    if record_path and os.path.exists(record_path):
        # 创建新的实验记录器实例，使用选定的记录文件
        recorder = ExperimentRecorder(record_file=record_path)

        # 更新全局实验管理器
        global experiment_manager
        experiment_manager = ExperimentManager(recorder=recorder)

        return jsonify({'success': True, 'message': f'已选择实验记录: {os.path.basename(record_path)}'})
    else:
        return jsonify({'success': False, 'message': '无效的实验记录路径'})

@app.route('/experiments')
def experiments():
    """实验列表页面"""
    # 获取所有实验环境
    processor = ExperimentProcessor()
    experiment_files = processor.find_experiment_files()

    # 提取环境ID和文件路径
    environments = []
    for file_path in experiment_files:
        env_id = os.path.basename(os.path.dirname(file_path)).replace("env_", "")
        environments.append({
            'id': env_id,
            'path': file_path,
            'name': f"环境 {env_id}"
        })

    # 按ID排序
    environments.sort(key=lambda x: int(x['id']))

    return render_template('experiments.html', environments=environments)

@app.route('/process_experiment/<env_id>', methods=['POST'])
def process_experiment(env_id):
    """处理单个实验"""
    try:
        result = experiment_manager.process_single_experiment(env_id)

        # 简化结果，移除mission_plans信息
        if 'mission_plans' in result:
            del result['mission_plans']

        # 简化分析结果，只保留任务ID和任务类型
        simplified_results = {}
        if 'analysis_results' in result:
            for task_id, task_data in result['analysis_results'].items():
                task_types = task_data.get('task_types', [])
                task_summary = task_data.get('task_summary', '')
                simplified_results[task_id] = {
                    'task_id': task_id,
                    'task_types': task_types,
                    'task_summary': task_summary[:100] + '...' if len(task_summary) > 100 else task_summary
                }
            result['analysis_results'] = simplified_results

        return jsonify({'success': True, 'message': f'环境 {env_id} 处理成功', 'result': result})
    except Exception as e:
        logger.error(f"处理环境 {env_id} 时出错: {str(e)}")
        return jsonify({'success': False, 'message': f'处理失败: {str(e)}'})

@app.route('/process_all_experiments', methods=['POST'])
def process_all_experiments():
    """处理所有实验"""
    try:
        results = experiment_manager.process_experiments()

        # 简化结果，移除mission_plans信息
        simplified_results = {}
        for env_id, env_data in results.items():
            simplified_env = {
                'env_id': env_id,
                'file_path': env_data.get('file_path', ''),
                'processing_time': env_data.get('processing_time', 0)
            }

            # 简化分析结果，只保留任务ID和任务类型
            if 'analysis_results' in env_data:
                simplified_analysis = {}
                for task_id, task_data in env_data['analysis_results'].items():
                    task_types = task_data.get('task_types', [])
                    task_summary = task_data.get('task_summary', '')
                    simplified_analysis[task_id] = {
                        'task_id': task_id,
                        'task_types': task_types,
                        'task_summary': task_summary[:100] + '...' if len(task_summary) > 100 else task_summary
                    }
                simplified_env['analysis_results'] = simplified_analysis

            simplified_results[env_id] = simplified_env

        return jsonify({'success': True, 'message': '所有环境处理成功', 'results': simplified_results})
    except Exception as e:
        logger.error(f"处理所有环境时出错: {str(e)}")
        return jsonify({'success': False, 'message': f'处理失败: {str(e)}'})

@app.route('/experiment_details/<env_id>')
def experiment_details(env_id):
    """实验详情页面"""
    # 获取实验数据
    experiment_data = {}

    # 尝试从记录器中获取数据
    try:
        json_path = experiment_manager.export_data("json")
        if os.path.exists(json_path):
            with open(json_path, 'r', encoding='utf-8') as f:
                all_data = json.load(f)
                if env_id in all_data:
                    experiment_data = all_data[env_id]
    except Exception as e:
        logger.error(f"获取环境 {env_id} 的数据时出错: {str(e)}")

    # 如果没有找到数据，尝试从文件中读取
    if not experiment_data:
        processor = ExperimentProcessor()
        experiment_files = processor.find_experiment_files()

        for file_path in experiment_files:
            file_env_id = os.path.basename(os.path.dirname(file_path)).replace("env_", "")
            if file_env_id == env_id:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        env_data = json.load(f)
                        experiment_data = {
                            'env_id': env_id,
                            'file_path': file_path,
                            'user_requirements': env_data.get('user_requirements', []),
                            'tasks': {}
                        }

                        # 检查是否有任务分析结果
                        task_analysis_dir = os.path.join(os.path.dirname(file_path), "task_analysis")
                        if os.path.exists(task_analysis_dir):
                            for task_file in os.listdir(task_analysis_dir):
                                if task_file.startswith("task_") and task_file.endswith(".json"):
                                    task_id = task_file.replace("task_", "").replace(".json", "")
                                    with open(os.path.join(task_analysis_dir, task_file), 'r', encoding='utf-8') as tf:
                                        task_data = json.load(tf)

                                        # 查找对应的用户需求
                                        requirement = None
                                        for req in experiment_data['user_requirements']:
                                            if req.get("task_id") == task_id:
                                                requirement = req
                                                break

                                        experiment_data['tasks'][task_id] = {
                                            'task_id': task_id,
                                            'requirement': requirement,
                                            'analysis_result': task_data,
                                            'llm_planning_time': None,
                                            'manual_planning_time': None,
                                            'expert_score': None
                                        }
                except Exception as e:
                    logger.error(f"读取环境 {env_id} 的文件时出错: {str(e)}")

    # 对用户需求按照任务ID的数字大小进行排序
    if 'user_requirements' in experiment_data:
        # 尝试将任务ID转换为整数进行排序
        def get_task_id_as_int(req):
            try:
                return int(req.get('task_id', '0'))
            except ValueError:
                return 0

        experiment_data['user_requirements'] = sorted(experiment_data['user_requirements'], key=get_task_id_as_int)

    return render_template('experiment_details.html', env_id=env_id, experiment_data=experiment_data)

@app.route('/record_manual_time', methods=['POST'])
def record_manual_time():
    """记录人工规划时间"""
    env_id = request.form.get('env_id')
    task_id = request.form.get('task_id')
    time_seconds = float(request.form.get('time_seconds'))

    try:
        experiment_manager.record_manual_planning_time(env_id, task_id, time_seconds)
        return jsonify({'success': True, 'message': f'已记录环境 {env_id} 任务 {task_id} 的人工规划时间: {time_seconds} 秒'})
    except Exception as e:
        logger.error(f"记录人工规划时间时出错: {str(e)}")
        return jsonify({'success': False, 'message': f'记录失败: {str(e)}'})

@app.route('/record_expert_score', methods=['POST'])
def record_expert_score():
    """记录专家评分"""
    env_id = request.form.get('env_id')
    task_id = request.form.get('task_id')
    score = int(request.form.get('score'))

    try:
        experiment_manager.record_expert_score(env_id, task_id, score)
        return jsonify({'success': True, 'message': f'已记录环境 {env_id} 任务 {task_id} 的专家评分: {score} 分'})
    except Exception as e:
        logger.error(f"记录专家评分时出错: {str(e)}")
        return jsonify({'success': False, 'message': f'记录失败: {str(e)}'})

@app.route('/update_task_description', methods=['POST'])
def update_task_description():
    """更新任务描述"""
    env_id = request.form.get('env_id')
    task_id = request.form.get('task_id')
    description = request.form.get('description')
    description_cn = request.form.get('description_cn')

    try:
        # 构建文件路径
        env_dir = os.path.join("experiment", f"env_{env_id}")
        file_path = os.path.join(env_dir, f"exp_env_{env_id}.json")

        if not os.path.exists(file_path):
            return jsonify({'success': False, 'message': f'找不到实验文件: {file_path}'})

        # 读取JSON文件
        with open(file_path, 'r', encoding='utf-8') as f:
            env_data = json.load(f)

        # 更新任务描述
        updated = False
        for req in env_data.get('user_requirements', []):
            if req.get('task_id') == task_id:
                req['description'] = description
                if description_cn:
                    req['description_cn'] = description_cn
                updated = True
                break

        if not updated:
            return jsonify({'success': False, 'message': f'找不到任务 {task_id}'})

        # 保存更新后的JSON文件
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(env_data, f, ensure_ascii=False, indent=2)

        return jsonify({'success': True, 'message': f'已更新环境 {env_id} 任务 {task_id} 的描述'})
    except Exception as e:
        logger.error(f"更新任务描述时出错: {str(e)}")
        return jsonify({'success': False, 'message': f'更新失败: {str(e)}'})

@app.route('/plan_single_task/<env_id>/<task_id>', methods=['POST'])
def plan_single_task(env_id, task_id):
    """单独规划任务"""
    try:
        # 构建文件路径
        env_dir = os.path.join("experiment", f"env_{env_id}")
        file_path = os.path.join(env_dir, f"exp_env_{env_id}.json")

        if not os.path.exists(file_path):
            return jsonify({'success': False, 'message': f'找不到实验文件: {file_path}'})

        # 读取JSON文件
        with open(file_path, 'r', encoding='utf-8') as f:
            env_data = json.load(f)

        # 查找任务
        task = None
        for req in env_data.get('user_requirements', []):
            if req.get('task_id') == task_id:
                task = req
                break

        if not task:
            return jsonify({'success': False, 'message': f'找不到任务 {task_id}'})

        # 获取任务先验信息
        mission_prior = env_data.get('mission_prior', {})

        # 创建输出目录
        output_dir = os.path.join(env_dir, "task_analysis")
        os.makedirs(output_dir, exist_ok=True)

        # 导入分析器模块
        from experiment_processor import ExperimentProcessor
        processor = ExperimentProcessor()

        # 导入分析器
        sys.path.append(os.path.dirname(processor.analyzer_path))
        import importlib.util
        spec = importlib.util.spec_from_file_location("drone_mission_analyzer", processor.analyzer_path)
        analyzer_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(analyzer_module)

        # 创建分析器实例
        analyzer = analyzer_module.DroneMissionAnalyzer()

        # 记录开始时间
        start_time = time.time()

        # 处理单个任务
        task_json = analyzer.process_task(task, mission_prior)

        # 记录结束时间
        end_time = time.time()
        processing_time = end_time - start_time

        # 添加处理时间
        task_json['processing_time'] = processing_time

        # 保存任务结果
        task_file = os.path.join(output_dir, f"task_{task_id}.json")
        with open(task_file, 'w', encoding='utf-8') as f:
            json.dump(task_json, f, ensure_ascii=False, indent=2)

        # 更新实验记录
        try:
            # 获取当前记录
            json_path = experiment_manager.export_data("json")
            if os.path.exists(json_path):
                with open(json_path, 'r', encoding='utf-8') as f:
                    all_data = json.load(f)

                # 更新任务数据
                if env_id in all_data and 'tasks' in all_data[env_id]:
                    if task_id in all_data[env_id]['tasks']:
                        all_data[env_id]['tasks'][task_id]['analysis_result'] = task_json
                        all_data[env_id]['tasks'][task_id]['llm_planning_time'] = processing_time
                    else:
                        # 查找对应的用户需求
                        requirement = None
                        for req in env_data.get('user_requirements', []):
                            if req.get('task_id') == task_id:
                                requirement = req
                                break

                        all_data[env_id]['tasks'][task_id] = {
                            'task_id': task_id,
                            'requirement': requirement,
                            'analysis_result': task_json,
                            'llm_planning_time': processing_time,
                            'manual_planning_time': None,
                            'expert_score': None
                        }

                # 保存更新后的记录
                with open(json_path, 'w', encoding='utf-8') as f:
                    json.dump(all_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"更新实验记录时出错: {str(e)}")

        return jsonify({
            'success': True,
            'message': f'已完成环境 {env_id} 任务 {task_id} 的规划，耗时 {processing_time:.2f} 秒',
            'processing_time': processing_time
        })
    except Exception as e:
        logger.error(f"规划任务时出错: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return jsonify({'success': False, 'message': f'规划失败: {str(e)}'})

@app.route('/analysis')
def analysis():
    """数据分析页面"""
    # 获取实验摘要
    summary = experiment_manager.get_experiment_summary()

    # 获取详细数据用于图表
    chart_data = {}
    try:
        json_path = experiment_manager.export_data("json")
        if os.path.exists(json_path):
            with open(json_path, 'r', encoding='utf-8') as f:
                all_data = json.load(f)

                # 准备时间对比数据
                time_comparison = []
                for env_id, env_data in all_data.items():
                    for task_id, task_data in env_data.get('tasks', {}).items():
                        llm_time = task_data.get('llm_planning_time')
                        manual_time = task_data.get('manual_planning_time')
                        if llm_time is not None:
                            time_comparison.append({
                                'env_id': env_id,
                                'task_id': task_id,
                                'type': 'LLM规划',
                                'time': llm_time
                            })
                        if manual_time is not None:
                            time_comparison.append({
                                'env_id': env_id,
                                'task_id': task_id,
                                'type': '人工规划',
                                'time': manual_time
                            })

                # 准备评分分布数据
                score_distribution = []
                for env_id, env_data in all_data.items():
                    for task_id, task_data in env_data.get('tasks', {}).items():
                        score = task_data.get('expert_score')
                        if score is not None:
                            score_distribution.append({
                                'env_id': env_id,
                                'task_id': task_id,
                                'score': score
                            })

                chart_data = {
                    'time_comparison': time_comparison,
                    'score_distribution': score_distribution
                }
    except Exception as e:
        logger.error(f"获取图表数据时出错: {str(e)}")

    return render_template('analysis.html', summary=summary, chart_data=chart_data)

@app.route('/export_data/<format_type>')
def export_data(format_type):
    """导出数据"""
    try:
        file_path = experiment_manager.export_data(format_type)
        if os.path.exists(file_path):
            return send_file(file_path, as_attachment=True)
        else:
            return jsonify({'success': False, 'message': f'导出失败: 文件不存在'})
    except Exception as e:
        logger.error(f"导出数据时出错: {str(e)}")
        return jsonify({'success': False, 'message': f'导出失败: {str(e)}'})

@app.route('/settings')
def settings():
    """设置页面"""
    return render_template('settings.html')

@app.route('/update_settings', methods=['POST'])
def update_settings():
    """更新设置"""
    api_key = request.form.get('api_key')
    model = request.form.get('model')
    api_url = request.form.get('api_url')
    experiment_dir = request.form.get('experiment_dir')
    output_dir = request.form.get('output_dir')

    # 创建新的实验管理器实例
    global experiment_manager
    experiment_manager = ExperimentManager(
        api_key=api_key,
        model=model,
        api_url=api_url,
        experiment_dir=experiment_dir if experiment_dir else "experiment",
        output_dir=output_dir if output_dir else "experiment_results"
    )

    return jsonify({'success': True, 'message': '设置已更新'})

@app.route('/system_info')
def system_info():
    """获取系统信息"""
    import platform
    import sys
    import flask

    return jsonify({
        'python_version': platform.python_version(),
        'flask_version': flask.__version__,
        'os_info': f"{platform.system()} {platform.release()}"
    })

if __name__ == '__main__':
    app.run(debug=True, port=5000)
