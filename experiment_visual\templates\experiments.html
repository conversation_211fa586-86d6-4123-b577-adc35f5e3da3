<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实验管理 - 无人机任务规划实验系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .env-card {
            transition: transform 0.3s;
        }
        .env-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .loading {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 9999;
            justify-content: center;
            align-items: center;
            color: white;
        }
        .spinner-border {
            width: 3rem;
            height: 3rem;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">无人机任务规划实验系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/experiments">实验管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/analysis">数据分析</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/settings">系统设置</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 加载指示器 -->
    <div class="loading" id="loadingIndicator">
        <div class="text-center">
            <div class="spinner-border mb-3" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <h5>处理中，请稍候...</h5>
            <p>这可能需要几分钟时间</p>
        </div>
    </div>

    <!-- 主要内容 -->
    <main class="container py-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>实验环境管理</h1>
            <button class="btn btn-primary" id="processAllBtn">
                <i class="bi bi-play-fill"></i> 处理所有实验
            </button>
        </div>

        <!-- 提示消息 -->
        <div class="alert alert-info" role="alert">
            <i class="bi bi-info-circle-fill me-2"></i>
            选择一个实验环境进行处理，或点击"处理所有实验"按钮一次性处理所有环境。
        </div>

        <!-- 实验环境列表 -->
        <div class="row g-4 mt-3">
            {% if environments %}
                {% for env in environments %}
                <div class="col-md-4">
                    <div class="card env-card h-100">
                        <div class="card-body">
                            <h5 class="card-title">{{ env.name }}</h5>
                            <p class="card-text text-muted small">{{ env.path }}</p>
                            <div class="d-flex justify-content-between mt-4">
                                <a href="/experiment_details/{{ env.id }}" class="btn btn-outline-primary">
                                    <i class="bi bi-eye"></i> 查看详情
                                </a>
                                <button class="btn btn-success process-btn" data-env-id="{{ env.id }}">
                                    <i class="bi bi-play-fill"></i> 处理
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="col-12">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        未找到任何实验环境。请确保experiment目录中包含exp_env_*.json文件。
                    </div>
                </div>
            {% endif %}
        </div>
    </main>

    <!-- 结果模态框 -->
    <div class="modal fade" id="resultModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">处理结果</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="resultMessage" class="alert mb-3"></div>
                    <div id="resultDetails">
                        <h6>详细信息：</h6>
                        <pre class="bg-light p-3 rounded"><code id="resultJson"></code></pre>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <a id="viewDetailsBtn" href="#" class="btn btn-primary">查看详情</a>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">© 2025 无人机任务规划实验系统 | 版权所有</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loadingIndicator = document.getElementById('loadingIndicator');
            const resultModal = new bootstrap.Modal(document.getElementById('resultModal'));
            const resultMessage = document.getElementById('resultMessage');
            const resultJson = document.getElementById('resultJson');
            const viewDetailsBtn = document.getElementById('viewDetailsBtn');
            
            // 处理单个实验
            document.querySelectorAll('.process-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const envId = this.getAttribute('data-env-id');
                    loadingIndicator.style.display = 'flex';
                    
                    fetch(`/process_experiment/${envId}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        loadingIndicator.style.display = 'none';
                        
                        if (data.success) {
                            resultMessage.className = 'alert alert-success mb-3';
                            viewDetailsBtn.href = `/experiment_details/${envId}`;
                        } else {
                            resultMessage.className = 'alert alert-danger mb-3';
                            viewDetailsBtn.href = '#';
                        }
                        
                        resultMessage.textContent = data.message;
                        resultJson.textContent = JSON.stringify(data.result, null, 2);
                        resultModal.show();
                    })
                    .catch(error => {
                        loadingIndicator.style.display = 'none';
                        resultMessage.className = 'alert alert-danger mb-3';
                        resultMessage.textContent = '处理失败: ' + error.message;
                        resultJson.textContent = '';
                        resultModal.show();
                    });
                });
            });
            
            // 处理所有实验
            document.getElementById('processAllBtn').addEventListener('click', function() {
                loadingIndicator.style.display = 'flex';
                
                fetch('/process_all_experiments', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    loadingIndicator.style.display = 'none';
                    
                    if (data.success) {
                        resultMessage.className = 'alert alert-success mb-3';
                        viewDetailsBtn.href = '/analysis';
                    } else {
                        resultMessage.className = 'alert alert-danger mb-3';
                        viewDetailsBtn.href = '#';
                    }
                    
                    resultMessage.textContent = data.message;
                    resultJson.textContent = JSON.stringify(data.results, null, 2);
                    resultModal.show();
                })
                .catch(error => {
                    loadingIndicator.style.display = 'none';
                    resultMessage.className = 'alert alert-danger mb-3';
                    resultMessage.textContent = '处理失败: ' + error.message;
                    resultJson.textContent = '';
                    resultModal.show();
                });
            });
        });
    </script>
</body>
</html>
