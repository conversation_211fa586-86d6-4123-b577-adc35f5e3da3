#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
实验管理器
整合实验数据处理和记录功能，提供命令行接口
"""

import os
import sys
import argparse
import logging
import json
import time
from typing import Dict, List, Any, Optional

from experiment_processor import ExperimentProcessor
from experiment_recorder import ExperimentRecorder

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("ExperimentManager")

class ExperimentManager:
    """实验管理器，整合实验数据处理和记录功能"""

    def __init__(self, experiment_dir: str = "experiment",
                 output_dir: str = "experiment_results",
                 api_key: str = None,
                 model: str = None,
                 api_url: str = None,
                 recorder: ExperimentRecorder = None):
        """
        初始化实验管理器

        Args:
            experiment_dir: 实验数据目录
            output_dir: 输出目录
            api_key: LLM API密钥
            model: LLM模型名称
            api_url: LLM API URL
            recorder: 已初始化的实验记录器实例，如果提供则使用该实例
        """
        self.experiment_dir = experiment_dir
        self.output_dir = output_dir

        # 初始化处理器
        self.processor = ExperimentProcessor(
            experiment_dir=experiment_dir,
            api_key=api_key,
            model=model,
            api_url=api_url
        )

        # 使用提供的记录器或创建新的记录器
        if recorder:
            self.recorder = recorder
            logger.info("使用提供的实验记录器实例")
        else:
            self.recorder = ExperimentRecorder(output_dir=output_dir)
            logger.info(f"创建新的实验记录器，输出目录: {output_dir}")

    def process_experiments(self):
        """处理所有实验"""
        logger.info("开始处理实验数据")

        # 处理实验数据
        results = self.processor.process_all_experiments()

        # 记录实验数据
        for env_id, data in results.items():
            self.recorder.record_experiment(env_id, data)

        logger.info("实验数据处理完成")
        return results

    def record_manual_planning_time(self, env_id: str, task_id: str, time_seconds: float):
        """
        记录人工规划时间

        Args:
            env_id: 环境ID
            task_id: 任务ID
            time_seconds: 人工规划时间（秒）
        """
        self.recorder.record_manual_planning_time(env_id, task_id, time_seconds)

    def record_expert_score(self, env_id: str, task_id: str, score: int):
        """
        记录专家评分

        Args:
            env_id: 环境ID
            task_id: 任务ID
            score: 专家评分（1-10分）
        """
        self.recorder.record_expert_score(env_id, task_id, score)

    def get_experiment_summary(self) -> Dict[str, Any]:
        """
        获取实验摘要

        Returns:
            实验摘要数据
        """
        return self.recorder.get_experiment_summary()

    def export_data(self, format_type: str = "json") -> str:
        """
        导出实验数据

        Args:
            format_type: 导出格式，支持"json"和"csv"

        Returns:
            导出文件的路径
        """
        return self.recorder.export_data(format_type)

    def process_single_experiment(self, env_id: str) -> Dict[str, Any]:
        """
        处理单个实验

        Args:
            env_id: 环境ID，例如"1"表示env_1

        Returns:
            处理结果
        """
        logger.info(f"处理环境 {env_id}")

        # 构建文件路径
        env_dir = os.path.join(self.experiment_dir, f"env_{env_id}")
        file_path = os.path.join(env_dir, f"exp_env_{env_id}.json")

        if not os.path.exists(file_path):
            logger.error(f"找不到实验文件: {file_path}")
            return {"error": f"找不到实验文件: {file_path}"}

        try:
            # 处理实验文件
            analysis_results, processing_time = self.processor.process_experiment_file(file_path)

            # 生成任务计划
            mission_plans = self.processor.generate_mission_plan(env_dir)

            # 记录结果
            result = {
                "analysis_results": analysis_results,
                "mission_plans": mission_plans,
                "processing_time": processing_time,
                "file_path": file_path
            }

            # 记录实验数据
            self.recorder.record_experiment(env_id, result)

            logger.info(f"环境 {env_id} 处理完成，耗时 {processing_time:.2f} 秒")
            return result
        except Exception as e:
            logger.error(f"处理环境 {env_id} 时出错: {str(e)}")
            return {"error": str(e), "file_path": file_path}

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='实验数据处理和记录系统')
    parser.add_argument('--experiment_dir', default="experiment", help='实验数据目录')
    parser.add_argument('--output_dir', default="experiment_results", help='输出目录')
    parser.add_argument('--api_key', help='LLM API密钥')
    parser.add_argument('--model', help='LLM模型名称')
    parser.add_argument('--api_url', help='LLM API URL')
    parser.add_argument('--env_id', help='要处理的环境ID，例如"1"表示env_1，不指定则处理所有环境')
    parser.add_argument('--record_manual_time', action='store_true', help='记录人工规划时间')
    parser.add_argument('--record_expert_score', action='store_true', help='记录专家评分')
    parser.add_argument('--export', choices=['json', 'csv'], help='导出数据格式')
    parser.add_argument('--summary', action='store_true', help='显示实验摘要')

    args = parser.parse_args()

    # 创建实验管理器
    manager = ExperimentManager(
        experiment_dir=args.experiment_dir,
        output_dir=args.output_dir,
        api_key=args.api_key,
        model=args.model,
        api_url=args.api_url
    )

    # 处理实验
    if args.env_id:
        result = manager.process_single_experiment(args.env_id)
        print(f"环境 {args.env_id} 处理结果:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
    elif not (args.record_manual_time or args.record_expert_score or args.export or args.summary):
        manager.process_experiments()

    # 记录人工规划时间
    if args.record_manual_time:
        env_id = input("请输入环境ID: ")
        task_id = input("请输入任务ID: ")
        time_str = input("请输入人工规划时间（秒）: ")
        try:
            time_seconds = float(time_str)
            manager.record_manual_planning_time(env_id, task_id, time_seconds)
            print(f"已记录环境 {env_id} 任务 {task_id} 的人工规划时间: {time_seconds} 秒")
        except ValueError:
            print(f"无效的时间值: {time_str}")

    # 记录专家评分
    if args.record_expert_score:
        env_id = input("请输入环境ID: ")
        task_id = input("请输入任务ID: ")
        score_str = input("请输入专家评分（1-10分）: ")
        try:
            score = int(score_str)
            manager.record_expert_score(env_id, task_id, score)
            print(f"已记录环境 {env_id} 任务 {task_id} 的专家评分: {score} 分")
        except ValueError:
            print(f"无效的评分值: {score_str}")

    # 导出数据
    if args.export:
        file_path = manager.export_data(args.export)
        print(f"数据已导出到: {file_path}")

    # 显示摘要
    if args.summary:
        summary = manager.get_experiment_summary()
        print("实验摘要:")
        print(json.dumps(summary, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    main()
