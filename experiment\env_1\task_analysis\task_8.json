{"task_id": "8", "description": "Fly along the edge of the forest and make sure everything looks normal.", "home_position": [42.29617690980287, -82.6623000857478, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[42.29718194618623, -82.66589734800576], [42.29727802176437, -82.65925867248248], [42.293533588741575, -82.65931927207117], [42.29357539942106, -82.66618960254094]]}, "task_types": ["Corridor scan task"], "task_summary": "The user requested a drone flight along the edge of the forest to visually check for abnormalities. This is best classified as a corridor scan task, following the forest boundary. The forest area exists in the available target areas, so the task is feasible. The corridor width is not explicitly stated, but a reasonable default (30 meters) will be used for effective edge monitoring. All required parameters are set to ensure efficient and clear visual inspection along the forest edge.", "feasibility_analysis": "The requested task is feasible because the 'forest' area is present in the available target areas. The task can be executed as a corridor scan along the forest's boundary. Since the width is not specified, a standard corridor width is assumed to ensure comprehensive edge coverage. All necessary parameters for a corridor scan are provided.", "subtasks": [{"subtask_id": "1", "type": "Corridor scan task", "target_area_id": "area_3", "target_area_name": "forest", "feasible": true, "feasibility_reason": "The forest area exists and its boundary can be used for a corridor scan. The task description matches a corridor scan along the forest edge.", "geometry": {"type": "linestring", "coordinates": [[42.29602833822704, -82.6611113548279], [42.29603839499092, -82.65998294950887], [42.29463146264805, -82.65998912414624], [42.294610799330975, -82.66115630230765], [42.29602833822704, -82.6611113548279]]}, "parameters": {"width": 30, "frontal_overlap": 70, "lateral_overlap": 60, "gsd": 2.0}}]}