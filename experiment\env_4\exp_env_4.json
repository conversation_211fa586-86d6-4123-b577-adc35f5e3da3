{"mission_prior": {"home_position": [35.77505708, 120.00004629, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[35.77903794938272, 119.99628067016603], [35.77924689089658, 120.00357627868654], [35.77064606477553, 120.00417709350586], [35.77047195015259, 119.99679565429689]]}, "target_areas": [{"id": "area_1", "name": "main_entrance", "type": "entrance", "geometry": {"type": "polygon", "coordinates": [[35.774562, 119.997834], [35.774562, 119.99822], [35.773928, 119.99822], [35.773928, 119.997834]]}, "properties": {}}, {"id": "area_2", "name": "visitor_center", "type": "building", "geometry": {"type": "polygon", "coordinates": [[35.77482, 119.998645], [35.77482, 119.999203], [35.774382, 119.999203], [35.774382, 119.998645]]}, "properties": {"height": 8}}, {"id": "area_3", "name": "mountain_peak", "type": "natural", "geometry": {"type": "polygon", "coordinates": [[35.778354, 120.001316], [35.778354, 120.002518], [35.777526, 120.002518], [35.777526, 120.001316]]}, "properties": {"height": 120}}, {"id": "area_4", "name": "scenic_lake", "type": "water", "geometry": {"type": "polygon", "coordinates": [[35.776327, 120.000486], [35.776327, 120.001945], [35.775178, 120.001945], [35.775178, 120.000486]]}, "properties": {}}, {"id": "area_5", "name": "ancient_temple", "type": "historical", "geometry": {"type": "polygon", "coordinates": [[35.77731, 119.999568], [35.77731, 120.00004], [35.776953, 120.00004], [35.776953, 119.999568]]}, "properties": {"height": 15}}, {"id": "area_6", "name": "hiking_trail", "type": "path", "geometry": {"type": "linestring", "coordinates": [[35.774734, 119.999396], [35.775478, 120.000126], [35.776459, 120.000555], [35.77714, 120.001112], [35.777783, 120.001831]]}, "properties": {"width": 2}}, {"id": "area_7", "name": "lookout_pavilion", "type": "structure", "geometry": {"type": "polygon", "coordinates": [[35.778064, 120.000641], [35.778064, 120.000919], [35.777869, 120.000919], [35.777869, 120.000641]]}, "properties": {"height": 6}}, {"id": "area_8", "name": "waterfall", "type": "natural", "geometry": {"type": "polygon", "coordinates": [[35.776737, 120.002604], [35.776737, 120.002883], [35.776436, 120.002883], [35.776436, 120.002604]]}, "properties": {"height": 25}}, {"id": "area_9", "name": "main_parking_lot", "type": "parking", "geometry": {"type": "polygon", "coordinates": [[35.773718, 119.998409], [35.773718, 119.99916], [35.772982, 119.99916], [35.772982, 119.998409]]}, "properties": {}}, {"id": "area_10", "name": "souvenir_market", "type": "commercial", "geometry": {"type": "polygon", "coordinates": [[35.773582, 119.999396], [35.773582, 119.999868], [35.773172, 119.999868], [35.773172, 119.999396]]}, "properties": {"height": 5}}, {"id": "area_11", "name": "restaurant_area", "type": "commercial", "geometry": {"type": "polygon", "coordinates": [[35.773954, 120.00004], [35.773954, 120.000491], [35.773497, 120.000491], [35.773497, 120.00004]]}, "properties": {"height": 7}}, {"id": "area_12", "name": "scenic_bridge", "type": "structure", "geometry": {"type": "linestring", "coordinates": [[35.775521, 120.001402], [35.775993, 120.001638]]}, "properties": {"width": 3, "height": 8}}, {"id": "area_13", "name": "garden_plaza", "type": "landscaped", "geometry": {"type": "polygon", "coordinates": [[35.774863, 119.999868], [35.774863, 120.000362], [35.774391, 120.000362], [35.774391, 119.999868]]}, "properties": {}}, {"id": "area_14", "name": "hotel", "type": "accommodation", "geometry": {"type": "polygon", "coordinates": [[35.772736, 119.999643], [35.772736, 120.000169], [35.772258, 120.000169], [35.772258, 119.999643]]}, "properties": {"height": 20}}]}, "user_requirements": [{"task_id": "1", "description": "Inspect the condition of the hiking trail, focusing on potential hazards", "description_cn": "检查徒步小道的状况，重点关注潜在危险。"}, {"task_id": "2", "description": "Survey the visitor center roof for any structural issues.", "description_cn": "勘察游客中心屋顶，查找任何结构性问题。"}, {"task_id": "3", "description": "Capture high-resolution photos of the mountain peak from multiple angles for promotional materials.", "description_cn": "从多角度拍摄山峰的高分辨率照片，用于宣传材料。"}, {"task_id": "4", "description": "Map the scenic lake boundaries and check water levels with a ground sampling distance (GSD) of 3 cm/pixel.", "description_cn": "绘制景观湖的边界并检查水位，地面采样距离为3厘米/像素。"}, {"task_id": "5", "description": "Execute sequential navigation at 80m altitude through key attractions: [[35.774562, 119.998120], [35.776327, 120.001245], [35.777310, 119.999868], [35.776537, 120.002704]]", "description_cn": "以80米高度按顺序飞过主要景点。"}, {"task_id": "6", "description": "Perform a detailed inspection of the ancient temple with 70% frontal overlap and 50% side overlap.", "description_cn": "对古庙进行详细检查，前向重叠度70%，侧向重叠度50%。"}, {"task_id": "7", "description": "Survey the main parking lot to assess capacity.", "description_cn": "勘察主停车场，评估容量。"}, {"task_id": "8", "description": "Create a high-resolution map of the waterfall area with 2 cm/pixel GSD.", "description_cn": "创建瀑布区域的高分辨率地图，地面采样距离为2厘米/像素。"}, {"task_id": "9", "description": "Inspect the scenic bridge for any structural issues or maintenance needs.", "description_cn": "检查景观桥，查找任何结构问题或维护需求。"}, {"task_id": "10", "description": "Capture oblique images of the lookout pavilion from various angles.", "description_cn": "从不同角度拍摄观景亭的倾斜影像。"}, {"task_id": "11", "description": "Create a comprehensive orthomosaic map of the entire scenic area with a GSD of 8 cm/pixel.", "description_cn": "创建整个景区的综合正射影像图，地面采样距离为8厘米/像素。"}, {"task_id": "12", "description": "Assess the condition of the souvenir market.", "description_cn": "评估纪念品市场的状况。"}, {"task_id": "13", "description": "Perform a perimeter security check around the hotel and restaurant area.", "description_cn": "对酒店和餐厅区域进行周边安全检查。"}, {"task_id": "14", "description": "Document the current state of the garden plaza for landscaping maintenance planning.", "description_cn": "记录花园广场的当前状态，用于景观维护规划。"}, {"task_id": "15", "description": "Create a terrain model of the mountain area for trail planning and visitor information.", "description_cn": "创建山区地形模型，用于小径规划和游客信息。"}, {"task_id": "16", "description": "Monitor visitor distribution at main entrance during peak hours to improve crowd management.", "description_cn": "在高峰时段监测主入口的游客分布，以改善人流管理。"}]}