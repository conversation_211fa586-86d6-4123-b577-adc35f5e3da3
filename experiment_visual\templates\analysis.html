<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据分析 - 无人机任务规划实验系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <script src="https://cdn.plot.ly/plotly-2.20.0.min.js"></script>
    <style>
        .chart-container {
            height: 400px;
            margin-bottom: 2rem;
        }
        .summary-card {
            transition: transform 0.3s;
        }
        .summary-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .export-btn {
            margin-right: 0.5rem;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">无人机任务规划实验系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/experiments">实验管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/analysis">数据分析</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/settings">系统设置</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container py-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>实验数据分析</h1>
            <div>
                <a href="/export_data/csv" class="btn btn-outline-primary export-btn">
                    <i class="bi bi-file-earmark-spreadsheet"></i> 导出CSV
                </a>
                <a href="/export_data/json" class="btn btn-outline-primary export-btn">
                    <i class="bi bi-file-earmark-code"></i> 导出JSON
                </a>
            </div>
        </div>

        <!-- 实验摘要 -->
        <section class="mb-5">
            <h2 class="mb-4">实验摘要</h2>
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="card summary-card h-100">
                        <div class="card-body text-center">
                            <h3 class="display-4 text-primary">{{ summary.total_environments }}</h3>
                            <p class="card-text">实验环境数量</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card summary-card h-100">
                        <div class="card-body text-center">
                            <h3 class="display-4 text-primary">{{ summary.total_tasks }}</h3>
                            <p class="card-text">任务总数</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card summary-card h-100">
                        <div class="card-body text-center">
                            <h3 class="display-4 text-primary">{{ "%.1f"|format(summary.avg_expert_score) }}</h3>
                            <p class="card-text">平均专家评分</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row g-4 mt-3">
                <div class="col-md-6">
                    <div class="card summary-card h-100">
                        <div class="card-body text-center">
                            <h3 class="display-4 text-primary">{{ "%.2f"|format(summary.avg_llm_planning_time) }}</h3>
                            <p class="card-text">平均LLM规划时间 (秒)</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card summary-card h-100">
                        <div class="card-body text-center">
                            <h3 class="display-4 text-primary">{{ "%.2f"|format(summary.avg_manual_planning_time) }}</h3>
                            <p class="card-text">平均人工规划时间 (秒)</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 图表分析 -->
        <section class="mb-5">
            <h2 class="mb-4">图表分析</h2>
            
            <!-- 时间对比图表 -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">LLM规划时间 vs 人工规划时间</h5>
                </div>
                <div class="card-body">
                    <div id="timeComparisonChart" class="chart-container"></div>
                </div>
            </div>
            
            <!-- 评分分布图表 -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">专家评分分布</h5>
                </div>
                <div class="card-body">
                    <div id="scoreDistributionChart" class="chart-container"></div>
                </div>
            </div>
            
            <!-- 任务类型统计图表 -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">任务类型统计</h5>
                </div>
                <div class="card-body">
                    <div id="taskTypeChart" class="chart-container"></div>
                </div>
            </div>
            
            <!-- 可行性分析图表 -->
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0">任务可行性分析</h5>
                </div>
                <div class="card-body">
                    <div id="feasibilityChart" class="chart-container"></div>
                </div>
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">© 2025 无人机任务规划实验系统 | 版权所有</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 时间对比图表
            const timeComparisonData = {{ chart_data.time_comparison|tojson }};
            if (timeComparisonData && timeComparisonData.length > 0) {
                const timeData = {
                    x: timeComparisonData.map(item => `环境${item.env_id}-任务${item.task_id}`),
                    y: timeComparisonData.map(item => item.time),
                    type: 'bar',
                    marker: {
                        color: timeComparisonData.map(item => item.type === 'LLM规划' ? '#0d6efd' : '#fd7e14')
                    },
                    text: timeComparisonData.map(item => item.type),
                    hovertemplate: '%{text}<br>时间: %{y} 秒<extra></extra>'
                };
                
                const timeLayout = {
                    title: 'LLM规划时间 vs 人工规划时间',
                    xaxis: {
                        title: '任务',
                        tickangle: -45
                    },
                    yaxis: {
                        title: '时间 (秒)'
                    },
                    showlegend: false
                };
                
                Plotly.newPlot('timeComparisonChart', [timeData], timeLayout);
            } else {
                document.getElementById('timeComparisonChart').innerHTML = '<div class="alert alert-warning">暂无数据</div>';
            }
            
            // 评分分布图表
            const scoreDistributionData = {{ chart_data.score_distribution|tojson }};
            if (scoreDistributionData && scoreDistributionData.length > 0) {
                // 计算每个评分的数量
                const scoreCounts = {};
                for (let i = 1; i <= 10; i++) {
                    scoreCounts[i] = 0;
                }
                
                scoreDistributionData.forEach(item => {
                    scoreCounts[item.score] = (scoreCounts[item.score] || 0) + 1;
                });
                
                const scoreData = {
                    x: Object.keys(scoreCounts).map(Number),
                    y: Object.values(scoreCounts),
                    type: 'bar',
                    marker: {
                        color: '#198754'
                    },
                    hovertemplate: '评分: %{x}<br>数量: %{y}<extra></extra>'
                };
                
                const scoreLayout = {
                    title: '专家评分分布',
                    xaxis: {
                        title: '评分',
                        tickmode: 'linear',
                        tick0: 1,
                        dtick: 1
                    },
                    yaxis: {
                        title: '数量'
                    }
                };
                
                Plotly.newPlot('scoreDistributionChart', [scoreData], scoreLayout);
            } else {
                document.getElementById('scoreDistributionChart').innerHTML = '<div class="alert alert-warning">暂无数据</div>';
            }
            
            // 任务类型统计和可行性分析图表
            // 这些图表需要从后端获取更多数据，这里只是示例
            // 实际实现时需要根据实际数据结构进行调整
            
            // 示例任务类型统计
            const taskTypeData = {
                labels: ['Survey task', 'Corridor scan task', 'Structure scan task', 'Simple waypoint task'],
                values: [12, 8, 5, 3],
                type: 'pie',
                textinfo: 'label+percent',
                insidetextorientation: 'radial'
            };
            
            const taskTypeLayout = {
                title: '任务类型统计'
            };
            
            Plotly.newPlot('taskTypeChart', [taskTypeData], taskTypeLayout);
            
            // 示例可行性分析
            const feasibilityData = {
                labels: ['完全可行', '部分可行', '不可行'],
                values: [15, 8, 5],
                type: 'pie',
                marker: {
                    colors: ['#198754', '#fd7e14', '#dc3545']
                },
                textinfo: 'label+percent',
                insidetextorientation: 'radial'
            };
            
            const feasibilityLayout = {
                title: '任务可行性分析'
            };
            
            Plotly.newPlot('feasibilityChart', [feasibilityData], feasibilityLayout);
        });
    </script>
</body>
</html>
