import json
import os
from flask import Flask, render_template, request, jsonify

app = Flask(__name__)
JSON_FILE_PATH = 'experiment/env_6/exp_env_6.json'

def load_geo_data():
    """加载地理数据文件"""
    with open(JSON_FILE_PATH, 'r', encoding='utf-8') as f:
        return json.load(f)

def save_geo_data(data):
    """保存地理数据到文件"""
    with open(JSON_FILE_PATH, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    return True

@app.route('/')
def index():
    """主页面"""
    return render_template('map.html')

@app.route('/api/geo-data')
def get_geo_data():
    """API端点：获取地理数据"""
    try:
        data = load_geo_data()
        return jsonify(data)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/update-coordinates', methods=['POST'])
def update_coordinates():
    """API端点：更新坐标数据"""
    try:
        data = request.json
        geo_data = load_geo_data()

        # 更新home_position
        if 'home_position' in data:
            geo_data['mission_prior']['home_position'] = data['home_position']

        # 更新flight_restricted_area
        if 'flight_restricted_area' in data:
            geo_data['mission_prior']['flight_restricted_area']['coordinates'] = data['flight_restricted_area']

        # 更新target_areas
        if 'target_areas' in data:
            for i, area in enumerate(data['target_areas']):
                if i < len(geo_data['mission_prior']['target_areas']):
                    geo_data['mission_prior']['target_areas'][i]['geometry']['coordinates'] = area['coordinates']

        # 保存更新后的数据
        save_geo_data(geo_data)
        return jsonify({"success": True})
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/measure-distance', methods=['POST'])
def measure_distance():
    """API端点：计算两点之间的距离"""
    try:
        data = request.json
        point1 = data['point1']  # [lat, lng]
        point2 = data['point2']  # [lat, lng]

        # 使用Haversine公式计算地理距离
        from math import radians, cos, sin, asin, sqrt

        # 将经纬度转换为弧度
        lat1, lon1 = radians(point1[0]), radians(point1[1])
        lat2, lon2 = radians(point2[0]), radians(point2[1])

        # Haversine公式
        dlon = lon2 - lon1
        dlat = lat2 - lat1
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
        c = 2 * asin(sqrt(a))
        r = 6371000  # 地球半径（米）
        distance = c * r

        return jsonify({"distance": distance})
    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    print("启动地理坐标可视化编辑器服务器...")
    print("请在浏览器中访问: http://127.0.0.1:5000")
    app.run(debug=True, use_reloader=False)
