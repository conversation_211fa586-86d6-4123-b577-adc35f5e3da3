<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统设置 - 无人机任务规划实验系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .settings-card {
            transition: transform 0.3s;
        }
        .settings-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .form-floating {
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">无人机任务规划实验系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/experiments">实验管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/analysis">数据分析</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/settings">系统设置</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container py-5">
        <h1 class="mb-4">系统设置</h1>

        <!-- LLM设置 -->
        <div class="card settings-card mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">LLM服务设置</h5>
            </div>
            <div class="card-body">
                <form id="llmSettingsForm">
                    <div class="form-floating">
                        <input type="text" class="form-control" id="apiKey" name="api_key" placeholder="API密钥">
                        <label for="apiKey">API密钥</label>
                        <div class="form-text">用于访问LLM服务的API密钥</div>
                    </div>
                    
                    <div class="form-floating">
                        <input type="text" class="form-control" id="model" name="model" placeholder="模型名称" value="openai/gpt-4.1">
                        <label for="model">模型名称</label>
                        <div class="form-text">要使用的LLM模型名称，例如：openai/gpt-4.1, anthropic/claude-3.7-sonnet</div>
                    </div>
                    
                    <div class="form-floating">
                        <input type="text" class="form-control" id="apiUrl" name="api_url" placeholder="API URL" value="https://openrouter.ai/api/v1/">
                        <label for="apiUrl">API URL</label>
                        <div class="form-text">LLM服务的API端点URL</div>
                    </div>
                    
                    <button type="button" class="btn btn-primary mt-3" id="saveSettingsBtn">保存设置</button>
                </form>
            </div>
        </div>

        <!-- 实验设置 -->
        <div class="card settings-card mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">实验设置</h5>
            </div>
            <div class="card-body">
                <form id="experimentSettingsForm">
                    <div class="form-floating">
                        <input type="text" class="form-control" id="experimentDir" name="experiment_dir" placeholder="实验数据目录" value="experiment">
                        <label for="experimentDir">实验数据目录</label>
                        <div class="form-text">存放实验数据的目录路径</div>
                    </div>
                    
                    <div class="form-floating">
                        <input type="text" class="form-control" id="outputDir" name="output_dir" placeholder="输出目录" value="experiment_results">
                        <label for="outputDir">输出目录</label>
                        <div class="form-text">存放实验结果的目录路径</div>
                    </div>
                    
                    <button type="button" class="btn btn-primary mt-3" id="saveExperimentSettingsBtn">保存设置</button>
                </form>
            </div>
        </div>

        <!-- 系统信息 -->
        <div class="card settings-card">
            <div class="card-header bg-light">
                <h5 class="mb-0">系统信息</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>系统版本:</strong> 1.0.0</p>
                        <p><strong>Python版本:</strong> <span id="pythonVersion">获取中...</span></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Flask版本:</strong> <span id="flaskVersion">获取中...</span></p>
                        <p><strong>操作系统:</strong> <span id="osInfo">获取中...</span></p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 结果提示 -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="resultToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <strong class="me-auto">操作结果</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body" id="toastMessage"></div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">© 2025 无人机任务规划实验系统 | 版权所有</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const resultToast = new bootstrap.Toast(document.getElementById('resultToast'));
            const toastMessage = document.getElementById('toastMessage');
            
            // 保存LLM设置
            document.getElementById('saveSettingsBtn').addEventListener('click', function() {
                const form = document.getElementById('llmSettingsForm');
                const formData = new FormData(form);
                
                fetch('/update_settings', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        toastMessage.textContent = data.message;
                        toastMessage.className = 'toast-body text-success';
                    } else {
                        toastMessage.textContent = data.message;
                        toastMessage.className = 'toast-body text-danger';
                    }
                    resultToast.show();
                })
                .catch(error => {
                    toastMessage.textContent = '操作失败: ' + error.message;
                    toastMessage.className = 'toast-body text-danger';
                    resultToast.show();
                });
            });
            
            // 保存实验设置
            document.getElementById('saveExperimentSettingsBtn').addEventListener('click', function() {
                const form = document.getElementById('experimentSettingsForm');
                const formData = new FormData(form);
                
                fetch('/update_settings', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        toastMessage.textContent = data.message;
                        toastMessage.className = 'toast-body text-success';
                    } else {
                        toastMessage.textContent = data.message;
                        toastMessage.className = 'toast-body text-danger';
                    }
                    resultToast.show();
                })
                .catch(error => {
                    toastMessage.textContent = '操作失败: ' + error.message;
                    toastMessage.className = 'toast-body text-danger';
                    resultToast.show();
                });
            });
            
            // 获取系统信息
            fetch('/system_info')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('pythonVersion').textContent = data.python_version;
                    document.getElementById('flaskVersion').textContent = data.flask_version;
                    document.getElementById('osInfo').textContent = data.os_info;
                })
                .catch(error => {
                    console.error('获取系统信息失败:', error);
                });
        });
    </script>
</body>
</html>
