{"task_id": "3", "description": "Survey the dormitory area to assess external building conditions and nearby facilities.", "home_position": [36.68293527764432, 117.17762231826784, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[36.68772685877701, 117.17438220977783], [36.687640962146325, 117.18077659606935], [36.67571771723314, 117.18521833419801], [36.67711084329782, 117.17431783676149]]}, "task_types": ["Survey task"], "task_summary": "The user requested a survey of the dormitory area to assess the external building conditions and nearby facilities. The dormitory area is a valid, available target area and can be surveyed as requested. The survey will focus on capturing the overall external conditions of the dormitory building and its immediate surroundings using standard mapping parameters. All requirements are feasible based on the provided data, and no parts of the task are infeasible. The mission will be executed as a survey task with parameters chosen for general mapping and condition assessment.", "feasibility_analysis": "The task is fully feasible. The dormitory area exists in the available target areas list, and its geometry and height are provided. The requirement to assess external building conditions and nearby facilities is best addressed by a survey task covering the dormitory area and its surroundings. No infeasible elements were identified.", "subtasks": [{"subtask_id": "1", "type": "Survey task", "target_area_id": "area_4", "target_area_name": "dormitory_area", "feasible": true, "feasibility_reason": "The dormitory area is present in the available target areas list with complete geometry and height information, making a survey task feasible.", "geometry": {"type": "polygon", "coordinates": [[36.684655918509605, 117.1771663427353], [36.68466444224811, 117.17742383480073], [36.68447501200689, 117.1774184703827], [36.68447067549101, 117.17715561389925]]}, "parameters": {"frontal_overlap": 75, "lateral_overlap": 60, "gsd": 2.0}}]}