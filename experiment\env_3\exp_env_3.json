{"mission_prior": {"home_position": [36.68293527764432, 117.17762231826784, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[36.68772685877701, 117.17438220977783], [36.687640962146325, 117.18077659606935], [36.67571771723314, 117.18521833419801], [36.67711084329782, 117.17431783676149]]}, "target_areas": [{"id": "area_1", "name": "library", "type": "building", "geometry": {"type": "polygon", "coordinates": [[36.678187687906515, 117.18101263046265], [36.67856612739797, 117.18177437782289], [36.67805834996039, 117.18223571777345], [36.67766273150595, 117.18148469924928]]}, "properties": {"height": 15}}, {"id": "area_2", "name": "teaching_building", "type": "building", "geometry": {"type": "polygon", "coordinates": [[36.68024313436918, 117.18204259872437], [36.680569959852235, 117.18287944793703], [36.67979537294124, 117.18333005905153], [36.679399887214934, 117.1825683116913]]}, "properties": {"height": 20}}, {"id": "area_3", "name": "laboratory_building", "type": "building", "geometry": {"type": "polygon", "coordinates": [[36.681799544748664, 117.1798861026764], [36.68178233279426, 117.18063712120056], [36.681275563282455, 117.18061566352846], [36.681257484665764, 117.1798861026764]]}, "properties": {"height": 18}}, {"id": "area_4", "name": "dormitory_area", "type": "building", "geometry": {"type": "polygon", "coordinates": [[36.684655918509605, 117.1771663427353], [36.68466444224811, 117.17742383480073], [36.68447501200689, 117.1774184703827], [36.68447067549101, 117.17715561389925]]}, "properties": {"height": 9}}, {"id": "area_5", "name": "cafeteria", "type": "building", "geometry": {"type": "polygon", "coordinates": [[36.68353694927753, 117.17615246772768], [36.68351974863444, 117.17742919921876], [36.68295181578062, 117.17743992805482], [36.68295181578062, 117.17614173889162]]}, "properties": {"height": 12}}, {"id": "area_6", "name": "track_field", "type": "sports", "geometry": {"type": "polygon", "coordinates": [[36.68701182991439, 117.17929601669313], [36.68700057924048, 117.18035817146303], [36.68537879391133, 117.18037962913515], [36.6853916940822, 117.17927992343904]]}, "properties": {}}, {"id": "area_7", "name": "football_field", "type": "sports", "geometry": {"type": "polygon", "coordinates": [[36.68675374050383, 117.1795427799225], [36.686746786132424, 117.18018651008607], [36.68558958016828, 117.18018651008607], [36.68560097667821, 117.17953205108644]]}, "properties": {}}, {"id": "area_8", "name": "basketball_courts", "type": "sports", "geometry": {"type": "polygon", "coordinates": [[36.68579146905999, 117.17681765556335], [36.685804369161644, 117.17740237712862], [36.685270955633385, 117.17740774154665], [36.68525805544227, 117.17681229114534]]}, "properties": {}}, {"id": "area_9", "name": "yingxue_lake", "type": "water", "geometry": {"type": "polygon", "coordinates": [[36.682737027923125, 117.17796564102174], [36.68272842751261, 117.17910289764406], [36.681420775275065, 117.17902779579164], [36.681425275805104, 117.17798709869386]]}, "properties": {}}, {"id": "area_10", "name": "main_road", "type": "road", "geometry": {"type": "linestring", "coordinates": [[36.68446610232326, 117.17749893665315], [36.68295674948406, 117.17763841152193], [36.68293094832333, 117.17923164367677]]}, "properties": {"width": 8}}, {"id": "area_11", "name": "campus_entrance", "type": "landmark", "geometry": {"type": "polygon", "coordinates": [[36.68380008507551, 117.17593252658844], [36.68415272897228, 117.17593252658844], [36.684152721690644, 117.17609882354736], [36.68380420682054, 117.17609882354736]]}, "properties": {}}]}, "user_requirements": [{"task_id": "1", "description": "Inspect the library roof for any water damage or structural issues.", "description_cn": "检查图书馆屋顶是否有水渍损坏或结构性问题。"}, {"task_id": "2", "description": "Check the condition of the football field and track to identify any areas needing maintenance.", "description_cn": "检查足球场和田径跑道的状况，找出任何需要维护的区域。"}, {"task_id": "3", "description": "Survey the dormitory area to assess external building conditions and nearby facilities.", "description_cn": "调查宿舍区，评估建筑外部状况和周边设施。"}, {"task_id": "4", "description": "Capture high-resolution photos of Yingxue Lake to monitor water levels and surrounding vegetation.", "description_cn": "拍摄映雪湖的高清照片，监测水位和周围植被。"}, {"task_id": "5", "description": "Create a detailed map of the basketball courts with a ground sampling distance (GSD) of 1.6 cm/pixel.", "description_cn": "创建篮球场的详细地图，地面采样距离(GSD)为1.6厘米/像素。"}, {"task_id": "6", "description": "Execute sequential navigation at 50m altitude through the following waypoints: [[36.682395, 117.177048], [36.684210, 117.177322], [36.684180, 117.179540], [36.681520, 117.179120]]", "description_cn": "以50米高度按顺序飞过以下航点。"}, {"task_id": "7", "description": "Perform a comprehensive scan of the teaching building exterior with 60% frontal overlap and 50% side overlap.", "description_cn": "对教学楼外部进行全面扫描，前向重叠度60%，侧向重叠度50%。"}, {"task_id": "8", "description": "Fly along the main road and capture photos to document campus traffic flow.", "description_cn": "沿着主干道飞行，拍摄照片以记录校园交通流量。"}, {"task_id": "9", "description": "Photograph the laboratory building from multiple angles to document its current state.", "description_cn": "从多个角度拍摄实验楼，记录其当前状态。"}, {"task_id": "10", "description": "Inspect the cafeteria roof and surrounding area for potential maintenance issues.", "description_cn": "检查食堂屋顶和周围区域是否有潜在的维护问题。"}, {"task_id": "11", "description": "Create an orthomosaic map of the entire campus with a GSD of 4 cm/pixel.", "description_cn": "创建整个校园的正射影像图，地面采样距离为4厘米/像素。"}, {"task_id": "12", "description": "Capture aerial photos of the campus entrance and main roads for campus navigation materials.", "description_cn": "拍摄校园入口和主要道路的航拍照片，用于校园导航材料。"}]}