{"mission_prior": {"home_position": [31.229976642679443, 121.47546529769899, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[31.23249044774377, 121.47368431091309], [31.231701752187035, 121.48143053054811], [31.224637968994486, 121.48048639297487], [31.22529811602503, 121.47276163101198]]}, "target_areas": [{"id": "area_1", "name": "residential_complex", "type": "building", "geometry": {"type": "polygon", "coordinates": [[31.230385066338826, 121.47668838500978], [31.22955032307603, 121.47657036781312], [31.22948619297669, 121.47772908210756], [31.230316475914076, 121.47783100605012]]}, "properties": {"height": 30}}, {"id": "area_2", "name": "office_tower", "type": "building", "geometry": {"type": "polygon", "coordinates": [[31.227967972724528, 121.47474110126497], [31.227261529692655, 121.4746016263962], [31.227142295189584, 121.47544384002686], [31.22786708783189, 121.47557258605958]]}, "properties": {"height": 40}}, {"id": "area_3", "name": "urban_park", "type": "greenspace", "geometry": {"type": "polygon", "coordinates": [[31.23054123772582, 121.47484838962556], [31.230472433899006, 121.47610902786256], [31.229552, 121.476038], [31.229594, 121.474789]]}, "properties": {}}, {"id": "area_4", "name": "shopping_mall", "type": "commercial", "geometry": {"type": "polygon", "coordinates": [[31.227981681837132, 121.4768385887146], [31.22790366296995, 121.477809548378], [31.227162, 121.477749], [31.227178, 121.476724]]}, "properties": {"height": 18}}, {"id": "area_5", "name": "construction_site", "type": "construction", "geometry": {"type": "polygon", "coordinates": [[31.226738605862632, 121.47511661052705], [31.226541410883012, 121.47596955299379], [31.225890023918357, 121.47586762905122], [31.2260138686233, 121.47497713565828]]}, "properties": {}}, {"id": "area_6", "name": "main_road", "type": "road", "geometry": {"type": "linestring", "coordinates": [[31.22943567856406, 121.47405982017519], [31.228738469305615, 121.47474110126497], [31.22866970687008, 121.47599637508394]]}, "properties": {"width": 20}}, {"id": "area_7", "name": "intersection", "type": "road", "geometry": {"type": "polygon", "coordinates": [[31.228843346428683, 121.47618949413301], [31.228829571444248, 121.47668838500978], [31.228357091722117, 121.47662401199342], [31.228352520095207, 121.47612512111665]]}, "properties": {}}, {"id": "area_8", "name": "urban_canal", "type": "water", "geometry": {"type": "linestring", "coordinates": [[31.231073462080587, 121.478168964386], [31.23123364674084, 121.4754331111908]]}, "properties": {"width": 15}}, {"id": "area_9", "name": "metro_station", "type": "transportation", "geometry": {"type": "polygon", "coordinates": [[31.228330370456458, 121.4791077375412], [31.22826612500461, 121.47966563701631], [31.228013755167137, 121.47964417934419], [31.22809174094474, 121.47910237312318]]}, "properties": {}}, {"id": "area_10", "name": "pedestrian_plaza", "type": "public_space", "geometry": {"type": "polygon", "coordinates": [[31.229325675371697, 121.47804021835329], [31.22932107661664, 121.47866785526277], [31.228774, 121.478618], [31.22879357122541, 121.47799193859102]]}, "properties": {}}, {"id": "area_11", "name": "bridge", "type": "building", "geometry": {"type": "polygon", "coordinates": [[31.231512767173037, 121.47661328315736], [31.231499010047006, 121.47679567337038], [31.230792676876938, 121.47669911384584], [31.23081102502442, 121.47652208805086]]}, "properties": {"height": 5}}]}, "user_requirements": [{"task_id": "1", "description": "Inspect the facilities and condition of the main intersection.", "description_cn": "检查主要十字路口的设施与状况。"}, {"task_id": "2", "description": "Perform facade inspection of the office tower with special attention to windows and exterior panels.", "description_cn": "对办公楼外立面进行检查，特别注意窗户和外部面板。"}, {"task_id": "3", "description": "Collect construction details from the site.", "description_cn": "收集建筑工地的施工细节信息。"}, {"task_id": "4", "description": "Observe the parking situation around the shopping mall.", "description_cn": "观察购物中心周围的停车状况。"}, {"task_id": "5", "description": "I want to check on the vegetation growth in the urban park.", "description_cn": "我想检查城市公园的植被长势。"}, {"task_id": "6", "description": "Inspect the surroundings of the residential for potential risks.", "description_cn": "检查住宅四周的潜在风险。"}, {"task_id": "7", "description": "Scan the urban canal for water quality assessment and potential blockages.", "description_cn": "对城市运河进行水质评估和潜在堵塞调查。"}, {"task_id": "8", "description": "Create promotional aerial footage of the pedestrian plaza for tourism purposes.", "description_cn": "为旅游宣传创建步行广场的航拍素材。"}, {"task_id": "9", "description": "Conduct aerial inspection along the main road.", "description_cn": "沿主路进行空中检查。"}, {"task_id": "10", "description": "Structural scanning of the bridge to support maintenance planning.", "description_cn": "对桥梁进行结构扫描，以支持维护规划。"}, {"task_id": "11", "description": "Perform a security sweep of the shopping mall and metro station.", "description_cn": "对购物中心和地铁站进行安全巡查。"}, {"task_id": "12", "description": "Create orthomosaic of the entire mission area with GSD of 7 cm/pixel.", "description_cn": "创建整个任务区域的高分辨率正射摄影图，地面分辨率为7厘米/像素。"}, {"task_id": "13", "description": "Survey rooftop solar panel installations on the office tower.", "description_cn": "检查办公楼楼顶的太阳能电池板安装情况。"}]}