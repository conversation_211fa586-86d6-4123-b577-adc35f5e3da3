{"mission_prior": {"home_position": [39.98709230924664, 120.45723438262941, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[39.991274, 120.453289], [39.991478, 120.460156], [39.984187, 120.460842], [39.983932, 120.453718]]}, "target_areas": [{"id": "area_1", "name": "main_production_hall", "type": "building", "geometry": {"type": "polygon", "coordinates": [[39.988754, 120.456124], [39.988754, 120.458213], [39.987328, 120.458213], [39.987328, 120.456124]]}, "properties": {"height": 15}}, {"id": "area_2", "name": "warehouse_1", "type": "building", "geometry": {"type": "polygon", "coordinates": [[39.989127, 120.455021], [39.989127, 120.455864], [39.988243, 120.455864], [39.988243, 120.455021]]}, "properties": {"height": 12}}, {"id": "area_3", "name": "warehouse_2", "type": "building", "geometry": {"type": "polygon", "coordinates": [[39.986521, 120.456873], [39.986521, 120.457792], [39.985732, 120.457792], [39.985732, 120.456873]]}, "properties": {"height": 12}}, {"id": "area_4", "name": "raw_material_storage", "type": "storage", "geometry": {"type": "polygon", "coordinates": [[39.986987, 120.455257], [39.986987, 120.455943], [39.986243, 120.455943], [39.986243, 120.455257]]}, "properties": {"height": 5}}, {"id": "area_5", "name": "finished_goods_area", "type": "storage", "geometry": {"type": "polygon", "coordinates": [[39.986123, 120.458428], [39.986123, 120.459281], [39.985324, 120.459281], [39.985324, 120.458428]]}, "properties": {}}, {"id": "area_6", "name": "loading_dock", "type": "logistics", "geometry": {"type": "polygon", "coordinates": [[39.985642, 120.458213], [39.985642, 120.458642], [39.984975, 120.458642], [39.984975, 120.458213]]}, "properties": {}}, {"id": "area_7", "name": "office_building", "type": "building", "geometry": {"type": "polygon", "coordinates": [[39.989876, 120.456542], [39.989876, 120.457243], [39.989327, 120.457243], [39.989327, 120.456542]]}, "properties": {"height": 25}}, {"id": "area_8", "name": "parking_lot", "type": "parking", "geometry": {"type": "polygon", "coordinates": [[39.990321, 120.456124], [39.990321, 120.457471], [39.989932, 120.457471], [39.989932, 120.456124]]}, "properties": {}}, {"id": "area_9", "name": "cooling_facility", "type": "equipment", "geometry": {"type": "polygon", "coordinates": [[39.987843, 120.458584], [39.987843, 120.458927], [39.987527, 120.458927], [39.987527, 120.458584]]}, "properties": {"height": 6}}, {"id": "area_10", "name": "solar_panel_installation", "type": "energy", "geometry": {"type": "polygon", "coordinates": [[39.988008477302884, 120.45497596263887], [39.98803312748835, 120.45572698116304], [39.987318049354435, 120.45579671859743], [39.9873016157264, 120.45502960681917]]}, "properties": {"height": 1}}, {"id": "area_11", "name": "perimeter_fence", "type": "security", "geometry": {"type": "linestring", "coordinates": [[39.99068428535162, 120.45460581779481], [39.99079935322956, 120.45973420143129], [39.984790365572536, 120.46000242233278], [39.98460950179297, 120.45461654663087], [39.99067606894301, 120.45457363128664]]}, "properties": {"width": 0.3}}, {"id": "area_12", "name": "main_access_road", "type": "road", "geometry": {"type": "linestring", "coordinates": [[39.990126, 120.455421], [39.990024, 120.457321], [39.988243, 120.457407], [39.985976, 120.45745]]}, "properties": {"width": 8}}, {"id": "area_13", "name": "waste_processing_unit", "type": "facility", "geometry": {"type": "polygon", "coordinates": [[39.985187, 120.456124], [39.985187, 120.456542], [39.984837, 120.456542], [39.984837, 120.456124]]}, "properties": {"height": 8}}, {"id": "area_14", "name": "buffer_zone", "type": "greenspace", "geometry": {"type": "polygon", "coordinates": [[39.990432, 120.458213], [39.990432, 120.459496], [39.989327, 120.459496], [39.989327, 120.458213]]}, "properties": {}}]}, "user_requirements": [{"task_id": "1", "description": "Inspect the main production hall roof for potential structural issues or water damage.", "description_cn": "检查主生产车间屋顶，排查潜在的结构问题或水损情况。"}, {"task_id": "2", "description": "Perform a detailed inspection of the solar panel installation to assess cleanliness and potential damage.", "description_cn": "详细检查太阳能板装置，评估清洁度和潜在损坏。"}, {"task_id": "3", "description": "Create a high-resolution map of the raw material storage area with a GSD of 2 cm/pixel.", "description_cn": "创建原材料存储区的高分辨率地图，地面采样距离为1厘米/像素。"}, {"task_id": "4", "description": "Fly the perimeter fence line to check for any security vulnerabilities or damage.", "description_cn": "沿着周界围栏飞行，检查是否存在安全漏洞或损坏。"}, {"task_id": "5", "description": "Execute sequential navigation at 40m altitude around key production facilities: [[39.988754, 120.456824], [39.987328, 120.457413], [39.986521, 120.457392], [39.985732, 120.457123]]", "description_cn": "以40米高度按顺序绕主要生产设施飞行。"}, {"task_id": "6", "description": "Inspect the cooling facility with 60% frontal overlap and 40% side overlap for comprehensive maintenance assessment.", "description_cn": "以60%前向重叠和40%侧向重叠检查冷却设施，进行全面维护评估。"}, {"task_id": "7", "description": "Create an orthomosaic of the entire factory premises with a GSD of 5 cm/pixel for facility planning.", "description_cn": "创建整个工厂区域的正射影像图，地面采样距离为5厘米/像素，用于设施规划。"}, {"task_id": "8", "description": "Perform a detailed survey of the loading dock area to optimize logistics operations.", "description_cn": "对装卸区进行详细测量，以优化物流运营。"}, {"task_id": "9", "description": "Inspect the warehouses for any roof damage or structural issues that could affect inventory.", "description_cn": "检查仓库是否有可能影响库存的屋顶损坏或结构问题。"}, {"task_id": "10", "description": "Map the parking lot to assess current capacity utilization and plan future expansion.", "description_cn": "测绘停车场，评估当前容量利用率并规划未来扩建。"}, {"task_id": "11", "description": "Inspect the waste processing unit for compliance with environmental regulations.", "description_cn": "检查废物处理装置是否符合环境法规。"}, {"task_id": "12", "description": "Document the current condition of the buffer zone vegetation.", "description_cn": "记录缓冲区植被的当前状况。"}, {"task_id": "13", "description": "Inspect the office building exterior with focus on windows, HVAC systems, and roof condition.", "description_cn": "检查办公楼外部，重点关注窗户、暖通空调系统和屋顶状况。"}, {"task_id": "14", "description": "Create high-precision 3D models of critical production equipment for maintenance planning.", "description_cn": "为关键生产设备创建高精度3D模型，用于维护规划。"}, {"task_id": "15", "description": "Conduct a thermal survey of the main production hall to identify potential energy loss areas.", "description_cn": "对主生产车间进行热成像测量，识别潜在的能量损失区域。"}]}