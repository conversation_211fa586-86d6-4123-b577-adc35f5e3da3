# 无人机任务分析与生成主程序

# 导入所需模块
from drone_mission_analyzer import analyze_mission
from drone_mission_generator import generate_mission

def main():
    """
    主函数：先执行任务分析，然后执行任务生成
    """
    print("===== 开始执行无人机任务分析 =====\n")
    # 执行任务分析
    analyze_mission()

    print("\n===== 任务分析完成，开始执行任务生成 =====\n")
    # 执行任务生成，处理task_analysis目录下的所有task_x.json文件
    generate_mission("task_analysis")

    print("\n===== 任务分析与生成全部完成 =====\n")

if __name__ == "__main__":
    main()
