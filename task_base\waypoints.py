def generate_waypoints_mission(waypoints, fly_height, start_seq_num):
    """
    生成航点任务项列表
    
    参数:
        waypoints (list): 航点坐标列表，每个元素为 [纬度, 经度]
        fly_height (float): 飞行高度
        start_seq_num (int): 起始序列号
    
    返回:
        tuple: (航点任务项列表, 任务项数量)
    """
    waypoints_items = []
    
    for i, waypoint in enumerate(waypoints):
        lat, lon = waypoint
        
        # 创建航点任务项
        item = {
            "AMSLAltAboveTerrain": None,
            "Altitude": fly_height,
            "AltitudeMode": 1,
            "autoContinue": True,
            "command": 16,
            "doJumpId": start_seq_num + i,
            "frame": 3,
            "params": [
                0,
                0,
                0,
                None,
                lat,
                lon,
                fly_height
            ],
            "type": "SimpleItem"
        }
        
        waypoints_items.append(item)
    
    return waypoints_items, len(waypoints_items)


# 示例使用
if __name__ == "__main__":
    # 输入参数
    waypoints = [
        [42.29619151778233, -82.66226119311997],
        [42.29592395, -82.66277059],
        [42.29549182, -82.66273773],
        [42.29536488, -82.66162774],
        [42.29601847, -82.66154741]
    ]
    fly_height = 37.6
    start_seq_num = 3
    
    # 生成航点任务项
    waypoints_items, item_count = generate_waypoints_mission(waypoints, fly_height, start_seq_num)
    
    # 打印结果
    import json
    print(f"任务项数量: {item_count}")
    print(json.dumps(waypoints_items, indent=4))