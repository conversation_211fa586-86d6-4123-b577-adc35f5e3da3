import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from pyulog import ULog
import numpy as np
import os
import matplotlib as mpl # 额外导入 matplotlib

# --- 全局字体设置 ---
# 指定一个支持中文的字体，例如 'SimHei' (黑体)
mpl.rcParams['font.sans-serif'] = ['SimHei']
# 解决保存图像时负号'-'显示为方块的问题
mpl.rcParams['axes.unicode_minus'] = False 

def plot_3d_trajectory(ulog_file_path):
    """
    解析 ULG 文件并绘制 3D 飞行轨迹。
    """
    # 检查文件是否存在
    if not os.path.exists(ulog_file_path):
        print(f"错误: 文件不存在 -> {ulog_file_path}")
        return

    # 加载 ULG 日志
    ulog = ULog(ulog_file_path)
    
    try:
        # 获取本地位置数据 (相对于起飞点)
        local_pos = ulog.get_dataset('vehicle_local_position').data
    except (<PERSON><PERSON><PERSON><PERSON>, IndexError):
        print("错误: 日志中未找到 'vehicle_local_position' 数据。")
        return

    # 提取 x, y, z 坐标
    x_coords = local_pos['x']
    y_coords = local_pos['y']
    z_coords = -local_pos['z'] # z 轴取反，变为 Altitude

    # --- 开始绘图 ---
    fig = plt.figure(figsize=(10, 8))
    ax = fig.add_subplot(111, projection='3d')

    # 绘制轨迹线
    ax.plot(x_coords, y_coords, z_coords, label='飞行轨迹')

    # 标记起点和终点
    ax.scatter(x_coords[0], y_coords[0], z_coords[0], color='green', s=50, label='起点')
    ax.scatter(x_coords[-1], y_coords[-1], z_coords[-1], color='red', s=50, label='终点')
    
    # 设置坐标轴标签
    ax.set_xlabel('X (米)')
    ax.set_ylabel('Y (米)')
    ax.set_zlabel('高度 (米)')

    # 设置图表标题
    ax.set_title('本发明实施例的无人机遥感飞行轨迹示意图')
    
    ax.grid(False) # 关闭背景网格线

    # 保持坐标轴比例一致，使轨迹不失真
    max_range = np.array([x_coords.max()-x_coords.min(), y_coords.max()-y_coords.min(), z_coords.max()-z_coords.min()]).max() / 2.0
    mid_x = (x_coords.max()+x_coords.min()) * 0.5
    mid_y = (y_coords.max()+y_coords.min()) * 0.5
    mid_z = (z_coords.max()+z_coords.min()) * 0.5
    ax.set_xlim(mid_x - max_range, mid_x + max_range)
    ax.set_ylim(mid_y - max_range, mid_y + max_range)
    ax.set_zlim(mid_z - max_range, mid_z + max_range)

    ax.legend()
    plt.show()

if __name__ == '__main__':
    # --- 请将这里替换为您的 ULG 文件路径 ---
    log_file = os.path.expanduser('01_44_57.ulg')
    plot_3d_trajectory(log_file)