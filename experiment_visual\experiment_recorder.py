#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
实验数据记录模块
用于记录实验数据，包括用户需求、LLM规划时间、人工规划时间和专家评分
"""

import os
import json
import time
import csv
import logging
from typing import Dict, List, Any, Optional
import sys
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("ExperimentRecorder")

class ExperimentRecorder:
    """实验数据记录类，用于记录实验数据"""

    def __init__(self, output_dir: str = "experiment_results", record_file: str = None):
        """
        初始化实验数据记录器

        Args:
            output_dir: 输出目录
            record_file: 现有记录文件路径，如果提供则使用该文件而不是创建新文件
        """
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)

        # 初始化数据结构
        self.experiment_data = {}

        if record_file and os.path.exists(record_file):
            # 使用现有记录文件
            self.record_file = record_file
            # 从文件名中提取时间戳
            base_name = os.path.basename(record_file)
            timestamp = base_name.replace("experiment_record_", "").replace(".json", "")
            self.csv_file = os.path.join(output_dir, f"experiment_summary_{timestamp}.csv")

            # 加载现有数据
            try:
                with open(record_file, 'r', encoding='utf-8') as f:
                    self.experiment_data = json.load(f)
                logger.info(f"已加载现有记录文件: {record_file}")
            except Exception as e:
                logger.error(f"加载记录文件失败: {str(e)}")
                self.experiment_data = {}
        else:
            # 创建新记录文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.record_file = os.path.join(output_dir, f"experiment_record_{timestamp}.json")
            self.csv_file = os.path.join(output_dir, f"experiment_summary_{timestamp}.csv")
            logger.info(f"已创建新记录文件: {self.record_file}")

    def record_experiment(self, env_id: str, experiment_data: Dict[str, Any]):
        """
        记录实验数据

        Args:
            env_id: 环境ID
            experiment_data: 实验数据，包含分析结果和处理时间
        """
        logger.info(f"记录环境 {env_id} 的实验数据")

        # 提取用户需求
        user_requirements = []
        file_path = experiment_data.get("file_path", "")

        try:
            if file_path and os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    env_data = json.load(f)
                    if "user_requirements" in env_data:
                        user_requirements = env_data["user_requirements"]
        except Exception as e:
            logger.error(f"读取用户需求时出错: {str(e)}")

        # 记录数据
        self.experiment_data[env_id] = {
            "env_id": env_id,
            "file_path": file_path,
            "processing_time": experiment_data.get("processing_time", 0),
            "user_requirements": user_requirements,
            "tasks": {},
            "timestamp": datetime.now().isoformat()
        }

        # 记录每个任务的数据
        if "analysis_results" in experiment_data:
            for task_id, task_data in experiment_data["analysis_results"].items():
                # 查找对应的用户需求
                requirement = None
                for req in user_requirements:
                    if req.get("task_id") == task_id:
                        requirement = req
                        break

                # 记录任务数据
                # 使用任务自己的处理时间，如果存在
                task_processing_time = task_data.get("processing_time", 0)
                if task_processing_time == 0:
                    # 如果任务没有自己的处理时间，则使用平均时间
                    task_processing_time = experiment_data.get("processing_time", 0) / len(experiment_data["analysis_results"]) if experiment_data["analysis_results"] else 0

                self.experiment_data[env_id]["tasks"][task_id] = {
                    "task_id": task_id,
                    "requirement": requirement,
                    "analysis_result": task_data,
                    "llm_planning_time": task_processing_time,
                    "manual_planning_time": None,  # 将由用户输入
                    "expert_score": None  # 将由专家评分
                }

        # 保存数据
        self._save_data()

    def record_manual_planning_time(self, env_id: str, task_id: str, time_seconds: float):
        """
        记录人工规划时间

        Args:
            env_id: 环境ID
            task_id: 任务ID
            time_seconds: 人工规划时间（秒）
        """
        logger.info(f"记录环境 {env_id} 任务 {task_id} 的人工规划时间: {time_seconds} 秒")

        if env_id in self.experiment_data and task_id in self.experiment_data[env_id]["tasks"]:
            self.experiment_data[env_id]["tasks"][task_id]["manual_planning_time"] = time_seconds
            self._save_data()
        else:
            logger.error(f"找不到环境 {env_id} 任务 {task_id} 的数据")

    def record_expert_score(self, env_id: str, task_id: str, score: int):
        """
        记录专家评分

        Args:
            env_id: 环境ID
            task_id: 任务ID
            score: 专家评分（1-10分）
        """
        if score < 1 or score > 10:
            logger.error(f"专家评分必须在1-10分之间，当前值: {score}")
            return

        logger.info(f"记录环境 {env_id} 任务 {task_id} 的专家评分: {score} 分")

        if env_id in self.experiment_data and task_id in self.experiment_data[env_id]["tasks"]:
            self.experiment_data[env_id]["tasks"][task_id]["expert_score"] = score
            self._save_data()
        else:
            logger.error(f"找不到环境 {env_id} 任务 {task_id} 的数据")

    def _save_data(self):
        """保存数据到JSON文件"""
        with open(self.record_file, 'w', encoding='utf-8') as f:
            json.dump(self.experiment_data, f, ensure_ascii=False, indent=2)
        logger.info(f"数据已保存到 {self.record_file}")

        # 同时更新CSV摘要
        self._update_csv_summary()

    def _update_csv_summary(self):
        """更新CSV摘要文件"""
        with open(self.csv_file, 'w', encoding='utf-8', newline='') as f:
            writer = csv.writer(f)

            # 写入表头
            writer.writerow([
                "环境ID", "任务ID", "任务描述", "LLM规划时间(秒)",
                "人工规划时间(秒)", "专家评分(1-10)", "任务类型", "可行性"
            ])

            # 写入数据
            for env_id, env_data in self.experiment_data.items():
                for task_id, task_data in env_data["tasks"].items():
                    requirement = task_data.get("requirement", {})
                    analysis = task_data.get("analysis_result", {})

                    description = requirement.get("description", "")
                    if "description_cn" in requirement:
                        description += f" ({requirement['description_cn']})"

                    # 获取任务类型和可行性
                    task_types = analysis.get("task_types", [])
                    task_type_str = ", ".join(task_types) if task_types else "未知"

                    # 检查可行性
                    feasible = "未知"
                    subtasks = analysis.get("subtasks", [])
                    if subtasks:
                        feasible_count = sum(1 for st in subtasks if st.get("feasible", False))
                        if feasible_count == 0:
                            feasible = "不可行"
                        elif feasible_count == len(subtasks):
                            feasible = "完全可行"
                        else:
                            feasible = f"部分可行 ({feasible_count}/{len(subtasks)})"

                    writer.writerow([
                        env_id,
                        task_id,
                        description,
                        task_data.get("llm_planning_time", ""),
                        task_data.get("manual_planning_time", ""),
                        task_data.get("expert_score", ""),
                        task_type_str,
                        feasible
                    ])

        logger.info(f"CSV摘要已更新到 {self.csv_file}")

    def export_data(self, format_type: str = "json") -> str:
        """
        导出实验数据

        Args:
            format_type: 导出格式，支持"json"和"csv"

        Returns:
            导出文件的路径
        """
        if format_type.lower() == "json":
            return self.record_file
        elif format_type.lower() == "csv":
            return self.csv_file
        else:
            logger.error(f"不支持的导出格式: {format_type}")
            return ""

    def get_experiment_summary(self) -> Dict[str, Any]:
        """
        获取实验摘要

        Returns:
            实验摘要数据
        """
        total_tasks = 0
        total_llm_time = 0
        total_manual_time = 0
        scored_tasks = 0
        total_score = 0

        for env_id, env_data in self.experiment_data.items():
            for task_id, task_data in env_data["tasks"].items():
                total_tasks += 1

                if task_data.get("llm_planning_time") is not None:
                    total_llm_time += task_data["llm_planning_time"]

                if task_data.get("manual_planning_time") is not None:
                    total_manual_time += task_data["manual_planning_time"]

                if task_data.get("expert_score") is not None:
                    scored_tasks += 1
                    total_score += task_data["expert_score"]

        return {
            "total_environments": len(self.experiment_data),
            "total_tasks": total_tasks,
            "avg_llm_planning_time": total_llm_time / total_tasks if total_tasks > 0 else 0,
            "avg_manual_planning_time": total_manual_time / total_tasks if total_tasks > 0 else 0,
            "avg_expert_score": total_score / scored_tasks if scored_tasks > 0 else 0,
            "scored_tasks_percentage": (scored_tasks / total_tasks * 100) if total_tasks > 0 else 0
        }

if __name__ == "__main__":
    # 简单测试
    recorder = ExperimentRecorder()
    print("实验数据记录器已初始化")
