<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地理坐标可视化编辑器</title>
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <!-- Leaflet Draw CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/leaflet.draw/1.0.4/leaflet.draw.css" />
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container">
        <header>
            <h1>地理坐标可视化编辑器</h1>
        </header>
        
        <div class="map-container">
            <div id="map"></div>
            
            <div class="sidebar">
                <div class="control-panel">
                    <h3>控制面板</h3>
                    
                    <div class="panel-section">
                        <h4>图层控制</h4>
                        <div id="layer-controls">
                            <!-- 图层控制将通过JS动态生成 -->
                        </div>
                    </div>
                    
                    <div class="panel-section">
                        <h4>测量工具</h4>
                        <button id="measure-distance" class="btn">测量距离</button>
                        <div id="measurement-result" class="result-box"></div>
                    </div>
                    
                    <div class="panel-section">
                        <h4>编辑操作</h4>
                        <button id="save-changes" class="btn primary">保存更改</button>
                        <button id="reset-changes" class="btn">重置更改</button>
                    </div>
                    
                    <div class="panel-section">
                        <h4>坐标信息</h4>
                        <div id="coordinate-info" class="info-box">
                            <p>选择地图上的点以查看和编辑坐标</p>
                        </div>
                    </div>
                </div>
                
                <div class="legend">
                    <h3>图例</h3>
                    <div class="legend-item">
                        <span class="legend-color" style="background-color: #3388ff;"></span>
                        <span>Home Position</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-color" style="background-color: #ff3333;"></span>
                        <span>Flight Restricted Area</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-color" style="background-color: #33cc33;"></span>
                        <span>建筑物 (Building)</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-color" style="background-color: #33ff99;"></span>
                        <span>绿地 (Greenspace)</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-color" style="background-color: #ff9900;"></span>
                        <span>商业区 (Commercial)</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-color" style="background-color: #cc9966;"></span>
                        <span>建筑工地 (Construction)</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-color" style="background-color: #999999;"></span>
                        <span>道路 (Road)</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-color" style="background-color: #3399ff;"></span>
                        <span>水域 (Water)</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-color" style="background-color: #9966ff;"></span>
                        <span>交通设施 (Transportation)</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-color" style="background-color: #ff99cc;"></span>
                        <span>公共空间 (Public Space)</span>
                    </div>
                </div>
            </div>
        </div>
        
        <footer>
            <p>地理坐标可视化编辑器 &copy; 2025</p>
        </footer>
    </div>

    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <!-- Leaflet Draw JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/leaflet.draw/1.0.4/leaflet.draw.js"></script>
    <!-- 自定义脚本 -->
    <script src="{{ url_for('static', filename='js/map_editor.js') }}"></script>
</body>
</html>
