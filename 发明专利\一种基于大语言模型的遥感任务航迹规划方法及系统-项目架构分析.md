# 基于大模型智能体的遥感任务航迹规划系统 - 项目架构分析

## 1. 项目概览

### 1.1 系统简介
本项目是一个基于大语言模型（LLM）智能体的无人机遥感任务航迹规划系统。系统能够理解自然语言描述的任务需求，智能分析任务可行性，并自动生成符合专业航拍标准的无人机飞行计划。

### 1.2 核心特性
- **智能任务分析**：使用GPT-4等大语言模型理解复杂的自然语言任务描述
- **多任务类型支持**：支持测绘、走廊扫描、结构扫描、航点导航四种任务类型
- **专业参数计算**：自动计算相机参数、重叠率、地面采样距离等专业参数
- **可行性评估**：智能评估任务可行性，提供详细的分析报告
- **标准化输出**：生成符合QGroundControl格式的飞行计划文件

### 1.3 技术架构
系统采用模块化设计，主要包含任务分析器、任务生成器和专业算法模块三大核心组件。

**系统整体架构如下图所示**：
- 用户通过自然语言描述任务需求
- 任务分析器使用大语言模型理解和分析任务
- 任务生成器根据分析结果调用相应的专业算法
- 最终生成标准化的QGroundControl飞行计划文件

**数据处理流程**：
系统采用两阶段处理模式：第一阶段进行智能任务分析，第二阶段进行具体的航线生成。这种设计确保了系统的可靠性和可维护性。

## 2. 目录结构分析

```
task_base/
├── main.py                          # 主程序入口
├── drone_mission_analyzer.py        # 任务分析器（LLM智能体）
├── drone_mission_generator.py       # 任务生成器
├── camera_calculate.py              # 相机参数计算模块
├── survey.py                        # 测绘任务算法
├── corridor_scan.py                 # 走廊扫描算法
├── structure_scan.py                # 结构扫描算法
├── waypoints.py                     # 航点任务算法
└── drone_mission_analyzer_deprecated.py  # 废弃版本

experiment/
├── env_1/                           # 实验环境1
│   ├── exp_env_1.json              # 环境配置文件
│   ├── task_analysis/              # 任务分析结果
│   │   └── task_1.json             # 具体任务分析
│   └── QGC_plan/                   # 生成的飞行计划
├── env_2/ ... env_6/               # 其他实验环境
```

## 3. 核心模块详细分析

### 3.1 主程序模块 (main.py)

**功能职责**：
- 系统入口点，协调任务分析和生成流程
- 实现两阶段处理：先分析后生成

**核心流程**：
```python
def main():
    # 阶段1：任务分析
    analyze_mission()
    
    # 阶段2：任务生成
    generate_mission("task_analysis")
```

**设计思想**：
采用管道式处理模式，将复杂的任务规划分解为分析和生成两个独立阶段，便于调试和维护。

### 3.2 任务分析器 (drone_mission_analyzer.py)

**功能职责**：
- 使用大语言模型理解自然语言任务描述
- 智能分类任务类型
- 评估任务可行性
- 生成结构化的任务分析结果

**核心类设计**：

#### DroneMissionAnalyzer类
```python
class DroneMissionAnalyzer:
    def __init__(self, api_key: str, model: str = "gpt-4o", base_url: str):
        # 初始化LLM客户端
        self.client = OpenAI(base_url=base_url, api_key=api_key)
```

**关键方法分析**：

1. **generate_prompt()** - 提示工程核心
   - 构建专业的航拍任务分析提示
   - 包含四种任务类型的详细定义
   - 提供专业的航拍参数标准
   - 实现智能参数选择指导

2. **call_llm_api()** - LLM接口调用
   - 封装OpenAI API调用
   - 设置合适的温度参数(0.3)确保输出稳定性
   - 异常处理和错误恢复

3. **parse_llm_response()** - 响应解析
   - 智能提取JSON格式的分析结果
   - 容错处理，确保系统稳定性
   - 回退机制，生成最小有效响应

**设计亮点**：
- **专业知识注入**：将航拍领域专业知识编码到提示中
- **智能参数选择**：避免边界值，根据上下文选择合适参数
- **可行性评估**：智能判断目标区域是否存在，任务是否可执行
- **多任务支持**：单个请求可包含多个子任务，分别处理

**核心代码示例**：
<augment_code_snippet path="task_base/drone_mission_analyzer.py" mode="EXCERPT">
````python
def generate_prompt(self, task_id: str, description: str, target_areas: List[Dict],
                    home_position: List, flight_restricted_area: Dict) -> str:
    # 创建简化的目标区域信息
    target_areas_simplified = []
    for area in target_areas:
        target_areas_simplified.append({
            "id": area["id"],
            "name": area["name"],
            "type": area["type"]
        })

    prompt = f"""
    You are an AI assistant specialized in drone aerial photography mission planning.

    Task Types:
    1. (Simple waypoint task) - Navigate between specific waypoints
    2. (Survey task) - Systematically scan an area for mapping or monitoring
    3. (Corridor scan task) - Scan along linear features (roads, pipelines, rivers)
    4. (Structure scan task) - Detailed scanning around buildings

    Professional Standards for Drone Aerial Photography Parameters:
    1. Frontal Overlap Rate: High-precision: 80%-90%, Standard: 70%-80%, Rapid: 55%-70%
    2. Lateral Overlap Rate: High-precision: 65%-80%, Standard: 55%-65%, Rapid: 35%-55%
    3. Ground Sampling Distance (GSD): High-precision: 0.5-1.0 cm/pixel, Medium: 1.0-2.5 cm/pixel
    """
    return prompt
````
</augment_code_snippet>

### 3.3 任务生成器 (drone_mission_generator.py)

**功能职责**：
- 将分析结果转换为具体的飞行任务
- 调用专业算法生成航点序列
- 构建完整的QGroundControl飞行计划

**核心函数分析**：

#### generate_drone_missions()
```python
def generate_drone_missions(task_json_path: str) -> List[Dict[str, Any]]:
    # 读取任务分析结果
    # 根据任务类型调用相应算法
    # 生成任务项列表
```

**任务类型处理逻辑**：
1. **Survey task** - 测绘任务
   - 验证多边形坐标有效性
   - 调用survey.py生成测绘航线
   - 计算相机触发参数

2. **Corridor scan task** - 走廊扫描
   - 处理线串坐标
   - 生成沿线扫描航线
   - 考虑走廊宽度参数

3. **Structure scan task** - 结构扫描
   - 围绕建筑物生成多层扫描航线
   - 考虑建筑高度参数
   - 实现立体扫描模式

4. **Simple waypoint task** - 航点任务
   - 简单的点到点导航
   - 设置统一飞行高度

#### build_complete_mission()
```python
def build_complete_mission(task_data: Dict, mission_items: List) -> Dict:
    # 添加起飞项
    # 处理任务项序列
    # 添加返航项
    # 构建地理围栏
    # 生成完整QGC格式文件
```

**设计特点**：
- **模块化调用**：每种任务类型对应独立的算法模块
- **参数验证**：严格验证必需参数，确保任务可执行性
- **错误处理**：完善的异常处理和用户友好的错误信息
- **标准化输出**：生成符合QGroundControl标准的.plan文件

**任务类型处理代码示例**：
<augment_code_snippet path="task_base/drone_mission_generator.py" mode="EXCERPT">
````python
# Survey task处理逻辑
if subtask_type == "Survey task":
    # 检查必要参数
    required_params = ['gsd', 'frontal_overlap', 'lateral_overlap']
    if not check_required_params(params, required_params, subtask_type, subtask_id):
        continue

    # 提取多边形坐标
    geometry = subtask.get('geometry', {})
    if not geometry or geometry.get('type') != 'polygon':
        print(f"错误: 子任务 {subtask_id} 缺少有效的多边形坐标")
        continue

    # 生成相机参数和测绘任务
    camera_calc = generate_camera_param(gsd, frontal_overlap, lateral_overlap)
    survey_items, item_count = generate_survey_mission(polygon, camera_calc, start_seq_num)
    mission_items.append(survey_items)
````
</augment_code_snippet>

### 3.4 相机参数计算模块 (camera_calculate.py)

**功能职责**：
- 计算相机成像参数
- 根据GSD和重叠率计算飞行参数
- 提供标准化的相机配置

**核心类设计**：
```python
class CameraCalc:
    def __init__(self):
        # Sony ILCE-QX1相机参数
        self.CameraName = "Sony ILCE-QX1"
        self.FocalLength = 16            # 焦距 (mm)
        self.SensorWidth = 23.2          # 传感器宽度 (mm)
        self.SensorHeight = 15.4         # 传感器高度 (mm)
        self.ImageWidth = 5456           # 图像宽度 (像素)
        self.ImageHeight = 3632          # 图像高度 (像素)
```

**关键算法**：
1. **影像密度计算**：
   ```python
   self.ImageDensity = (self.DistanceToSurface * self.SensorWidth * 100.0) / 
                       (self.ImageWidth * self.FocalLength)
   ```

2. **图像足迹计算**：
   - 考虑横向/竖向拍摄模式
   - 计算地面覆盖范围
   - 调整重叠率影响

**设计优势**：
- **专业相机模型**：基于真实相机参数
- **双向计算**：支持距离→密度和密度→距离两种计算模式
- **拍摄模式适配**：支持横向和竖向拍摄

**相机参数计算代码示例**：
<augment_code_snippet path="task_base/camera_calculate.py" mode="EXCERPT">
````python
class CameraCalc:
    def __init__(self):
        # Sony ILCE-QX1相机参数
        self.CameraName = "Sony ILCE-QX1"
        self.FocalLength = 16            # 焦距 (mm)
        self.SensorWidth = 23.2          # 传感器宽度 (mm)
        self.SensorHeight = 15.4         # 传感器高度 (mm)
        self.ImageWidth = 5456           # 图像宽度 (像素)
        self.ImageHeight = 3632          # 图像高度 (像素)

    def calculate_imaging_metrics(self):
        if self.ValueSetIsDistance:
            # 基于地面距离计算影像密度
            self.ImageDensity = (self.DistanceToSurface * self.SensorWidth * 100.0) / \
                               (self.ImageWidth * self.FocalLength)
        else:
            # 反向计算地面距离
            self.DistanceToSurface = (self.ImageWidth * self.ImageDensity * self.FocalLength) / \
                                   (self.SensorWidth * 100.0)
````
</augment_code_snippet>

## 4. 专业算法模块分析

### 4.1 测绘任务算法 (survey.py)

**核心功能**：
- 生成覆盖指定区域的栅格化航线
- 优化航线路径，减少转弯次数
- 计算相机触发距离

**关键算法**：
1. **区域栅格化**：将多边形区域分解为平行航线
2. **航线优化**：使用蛇形路径减少无效飞行
3. **边界处理**：确保完全覆盖目标区域

### 4.2 走廊扫描算法 (corridor_scan.py)

**核心功能**：
- 沿线性特征生成扫描航线
- 支持可变走廊宽度
- 处理复杂线形和转弯

**算法特点**：
- **自适应宽度**：根据走廊宽度动态调整航线间距
- **转弯优化**：在线路转弯处优化航线布局
- **边界约束**：确保航线不超出走廊范围

### 4.3 结构扫描算法 (structure_scan.py)

**核心功能**：
- 围绕建筑物生成多层扫描航线
- 实现立体化数据采集
- 优化拍摄角度和距离

**算法创新**：
- **多层扫描**：根据建筑高度生成多个高度层
- **环绕飞行**：围绕建筑物边界生成环形航线
- **角度优化**：确保最佳拍摄角度和重叠率

## 5. 数据流和控制流分析

### 5.1 数据流向
```
用户需求(自然语言) → 任务分析器(LLM) → 结构化任务描述 → 
任务生成器 → 专业算法模块 → QGC飞行计划文件
```

### 5.2 控制流程
1. **输入验证**：检查实验环境配置文件
2. **任务分析**：LLM理解和分类任务
3. **可行性评估**：验证目标区域和参数
4. **算法调用**：根据任务类型选择算法
5. **结果整合**：构建完整飞行计划
6. **文件输出**：生成标准格式文件

## 6. 实验配置系统

### 6.1 配置文件结构
```json
{
  "mission_prior": {
    "home_position": [纬度, 经度, 高度],
    "flight_restricted_area": {...},
    "target_areas": [...]
  },
  "user_requirements": [...]
}
```

### 6.2 目标区域定义
系统支持多种类型的目标区域：
- **建筑物**：house, warehouse, library等
- **自然区域**：forest, field, lake等  
- **基础设施**：road, pipeline等

## 7. 系统创新点和技术优势

### 7.1 核心创新
1. **LLM智能体架构**：首次将大语言模型应用于无人机任务规划
2. **自然语言理解**：支持复杂的自然语言任务描述
3. **智能参数选择**：基于上下文自动选择最优航拍参数
4. **多任务融合**：单次规划支持多种任务类型组合

### 7.2 技术优势
1. **专业知识集成**：将航拍领域专业知识编码到系统中
2. **模块化设计**：高度模块化，易于扩展和维护
3. **标准化输出**：生成业界标准的QGroundControl格式
4. **智能容错**：完善的错误处理和回退机制

### 7.3 应用价值
1. **降低技术门槛**：非专业用户也能规划复杂航拍任务
2. **提高效率**：自动化任务规划，大幅减少人工工作量
3. **保证质量**：基于专业标准，确保航拍质量
4. **扩展性强**：支持新任务类型和算法的快速集成

## 8. 发明专利建议

基于以上分析，建议专利申请重点关注以下技术创新点：

### 8.1 核心技术创新
1. **基于大语言模型的无人机任务智能分析方法**
   - 自然语言任务描述的理解和分类
   - 智能参数选择和优化算法
   - 多任务类型的统一处理框架

2. **遥感任务可行性智能评估系统**
   - 目标区域匹配算法
   - 任务约束条件验证
   - 智能回退和建议机制

3. **多模态航迹规划算法集成架构**
   - 测绘、走廊、结构、航点四种算法的统一接口
   - 任务参数的自动计算和优化
   - 标准化输出格式转换

### 8.2 专利申请策略
1. **主专利**：整体系统架构和核心算法
2. **分专利**：各个专业算法模块的具体实现
3. **应用专利**：特定应用场景的优化方案

### 8.3 技术保护重点
1. **LLM提示工程**：专业领域知识的编码方法
2. **智能参数选择**：基于上下文的参数优化算法
3. **多任务融合**：复杂任务的分解和重组方法
4. **标准化接口**：与现有航拍软件的兼容性设计

这个系统代表了人工智能在遥感领域应用的重要突破，具有很高的技术创新性和实用价值，非常适合申请发明专利保护。

## 9. 详细代码分析

### 9.1 LLM提示工程核心技术

系统的核心创新在于将航拍领域的专业知识通过提示工程注入到大语言模型中：

#### 专业参数标准定义
```python
# 前向重叠率标准
- High-precision: 80%-90% (考古遗址、详细结构分析)
- Standard: 70%-80% (一般测绘、地形建模)
- Rapid: 55%-70% (快速评估、初步调查)

# 侧向重叠率标准
- High-precision: 65%-80% (详细正射影像、精确体积计算)
- Standard: 55%-65% (地形测绘、一般场地记录)
- Rapid: 35%-55% (侦察、大面积覆盖)

# 地面采样距离(GSD)和对应飞行高度
- High-precision: 0.5-1.0 cm/pixel (飞行高度: 19-38m)
- Medium-precision: 1.0-2.5 cm/pixel (飞行高度: 38-94m)
- Low-precision: 2.5-8.0 cm/pixel (飞行高度: 94-300m)
```

#### 智能参数选择算法
系统避免总是选择边界值，而是根据任务描述的上下文智能选择：
- **任务紧急程度**：影响重叠率选择
- **目标区域复杂度**：影响GSD精度要求
- **时间约束**：影响扫描密度
- **分析目的**：影响参数精度需求

### 9.2 任务类型分类算法

#### 四种核心任务类型
1. **Simple waypoint task** - 点到点导航
   - 适用场景：简单的位置检查、设备巡检
   - 参数要求：仅需飞行高度
   - 几何类型：multipoint

2. **Survey task** - 区域测绘
   - 适用场景：地形测绘、农田监测、区域调查
   - 参数要求：frontal_overlap, lateral_overlap, gsd
   - 几何类型：polygon

3. **Corridor scan task** - 走廊扫描
   - 适用场景：道路巡检、管线监测、河流调查
   - 参数要求：frontal_overlap, lateral_overlap, gsd, width
   - 几何类型：linestring

4. **Structure scan task** - 结构扫描
   - 适用场景：建筑物检查、古迹记录、设施监测
   - 参数要求：frontal_overlap, lateral_overlap, gsd, height
   - 几何类型：polygon

### 9.3 相机参数计算核心算法

#### 影像密度计算公式
```python
# 基于飞行距离计算影像密度
ImageDensity = (DistanceToSurface * SensorWidth * 100.0) / (ImageWidth * FocalLength)

# 反向计算飞行距离
DistanceToSurface = (ImageWidth * ImageDensity * FocalLength) / (SensorWidth * 100.0)
```

#### 图像足迹计算
```python
# 横向拍摄模式
if Landscape:
    imageFootprintSide = (ImageWidth * ImageDensity) / 100.0
    imageFootprintFrontal = (ImageHeight * ImageDensity) / 100.0
else:
    # 竖向拍摄模式
    imageFootprintSide = (ImageHeight * ImageDensity) / 100.0
    imageFootprintFrontal = (ImageWidth * ImageDensity) / 100.0
```

#### 调整后足迹计算
```python
# 考虑重叠率的实际覆盖距离
AdjustedFootprintSide = imageFootprintSide * (100 - SideOverlap) / 100.0
AdjustedFootprintFrontal = imageFootprintFrontal * (100 - FrontalOverlap) / 100.0
```

### 9.4 测绘任务航线生成算法

#### 核心数据结构
```python
class CoordType(Enum):
    TURNAROUND = auto()               # 转弯点
    SURVEY_ENTRY = auto()             # 测量区域入口点
    SURVEY_EXIT = auto()              # 测量区域出口点
    INTERIOR_HOVER_TRIGGER = auto()   # 内部悬停触发点
    INTERIOR = auto()                 # 内部普通点
```

#### 航线生成策略
1. **区域边界分析**：计算多边形的最小外接矩形
2. **航线方向优化**：选择最优的扫描方向减少航线数量
3. **栅格化处理**：按照调整后足迹间距生成平行航线
4. **边界裁剪**：确保航线完全覆盖目标区域
5. **路径优化**：使用蛇形路径减少转弯次数

### 9.5 走廊扫描算法创新

#### 动态宽度处理
```python
def generate_corridor_scan_mission(polyline, camera_calc, corridor_width, start_seq_num):
    # 沿中心线生成偏移航线
    # 根据走廊宽度计算航线数量
    # 处理线路转弯和分叉
```

#### 关键技术点
1. **中心线提取**：从复杂线形中提取主要走向
2. **偏移航线生成**：在中心线两侧生成平行航线
3. **转弯处理**：在线路转弯处优化航线布局
4. **宽度自适应**：根据走廊宽度动态调整航线密度

### 9.6 结构扫描立体算法

#### 多层扫描策略
```python
def generate_structure_scan_mission(polygon, camera_calc, structure_height):
    # 根据建筑高度计算扫描层数
    # 每层生成环绕航线
    # 优化垂直间距和拍摄角度
```

#### 立体扫描创新
1. **高度分层**：根据建筑高度和相机参数计算最优层数
2. **环绕飞行**：围绕建筑物边界生成环形航线
3. **角度优化**：确保每个面都有足够的拍摄角度
4. **重叠保证**：垂直和水平方向都保证足够重叠

## 10. 系统集成和标准化

### 10.1 QGroundControl格式兼容

#### 标准任务项结构
```json
{
  "AMSLAltAboveTerrain": null,
  "Altitude": 30,
  "AltitudeMode": 1,
  "autoContinue": true,
  "command": 16,
  "doJumpId": 2,
  "frame": 3,
  "params": [0, 0, 0, null, 42.296, -82.662, 30],
  "type": "SimpleItem"
}
```

#### 完整任务文件结构
```json
{
  "fileType": "Plan",
  "geoFence": {...},           # 地理围栏
  "groundStation": "QGroundControl",
  "mission": {
    "cruiseSpeed": 15,
    "firmwareType": 12,
    "items": [...],            # 任务项列表
    "plannedHomePosition": [...],
    "vehicleType": 2,          # 多旋翼
    "version": 2
  },
  "rallyPoints": {...},        # 集结点
  "version": 1
}
```

### 10.2 地理围栏自动生成

系统自动将flight_restricted_area转换为QGC地理围栏：
```python
geofence = {
    "circles": [],
    "polygons": [{
        "inclusion": True,      # 包含区域（允许飞行）
        "polygon": coordinates,
        "version": 1
    }],
    "version": 2
}
```

## 11. 错误处理和容错机制

### 11.1 多层次错误处理

#### LLM响应解析容错
```python
def parse_llm_response(self, response: str, task_id: str, description: str) -> Dict:
    try:
        # 提取和解析JSON
        json_str = response[json_start:json_end]
        task_json = json.loads(json_str)

        # 结构验证和补全
        if "task_id" not in task_json:
            task_json["task_id"] = task_id

    except Exception as e:
        # 生成最小有效回退响应
        return {
            "task_id": task_id,
            "description": description,
            "task_types": ["unknown"],
            "task_summary": "无法完成任务分析...",
            "error": str(e)
        }
```

#### 参数验证机制
```python
def check_required_params(params: Dict, required_params: List, task_type: str, subtask_id: str) -> bool:
    missing_params = []
    for param in required_params:
        if param not in params:
            missing_params.append(param)

    if missing_params:
        print(f"错误: 子任务 {subtask_id} ({task_type}) 缺少必要参数: {', '.join(missing_params)}")
        return False
    return True
```

### 11.2 坐标数据验证

#### 多层次坐标处理
```python
# 处理不同嵌套层次的坐标数据
if isinstance(coordinates[0], list) and coordinates[0] and isinstance(coordinates[0][0], list):
    polygon = coordinates[0]  # 展平一层
else:
    polygon = coordinates

# 验证坐标格式
for i, coord in enumerate(polygon):
    if not isinstance(coord, list) or len(coord) != 2:
        print(f"错误: 坐标点 {i+1} 格式不正确: {coord}")
        return False
```

## 12. 性能优化和扩展性

### 12.1 模块化设计优势

#### 算法模块独立性
每个任务类型对应独立的算法模块，便于：
- **并行开发**：不同算法可以独立开发和测试
- **性能优化**：针对特定任务类型进行专门优化
- **功能扩展**：新增任务类型只需添加新模块
- **维护升级**：模块间低耦合，便于维护

#### 标准化接口设计
```python
# 统一的算法接口
def generate_xxx_mission(geometry, camera_calc, specific_params, start_seq_num):
    # 返回: (mission_items, item_count)
    return mission_items, len(mission_items)
```

### 12.2 扩展性设计

#### 新任务类型扩展
添加新任务类型只需：
1. 在LLM提示中添加任务类型定义
2. 创建对应的算法模块
3. 在任务生成器中添加调用逻辑

#### 新算法集成
系统支持算法的热插拔：
- **接口标准化**：统一的输入输出格式
- **参数标准化**：统一的参数验证机制
- **错误处理标准化**：统一的异常处理流程

## 13. 实际应用案例分析

### 13.1 森林监测任务案例

基于task_1.json的实际案例：

#### 任务描述
"Assist in rapidly monitoring the nearby forest areas."

#### 系统分析过程
1. **任务理解**：识别为快速森林区域监测需求
2. **目标匹配**：在target_areas中找到"forest"区域
3. **任务分类**：归类为Survey task（测绘任务）
4. **参数选择**：
   - frontal_overlap: 65% (快速监测级别)
   - lateral_overlap: 45% (快速监测级别)
   - gsd: 5.0 cm/pixel (中等精度)

#### 生成结果
- **可行性**：feasible: true
- **几何信息**：forest区域的polygon坐标
- **飞行计划**：包含起飞、测绘航线、返航的完整任务

### 13.2 系统智能化体现

#### 上下文理解能力
- **关键词识别**："rapidly"→选择快速监测参数
- **目标匹配**："forest areas"→匹配到area_3森林区域
- **任务推理**："monitoring"→推断为Survey task

#### 专业参数选择
- **避免极值**：不选择边界值65%和70%，而是选择65%
- **平衡考虑**：在速度和质量间找到平衡点
- **实用导向**：参数选择考虑实际应用需求

这个详细的技术分析展示了系统的创新性和实用性，为发明专利申请提供了强有力的技术支撑。
