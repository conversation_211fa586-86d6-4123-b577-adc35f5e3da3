# 实验数据处理和记录系统

本系统用于处理无人机任务规划实验数据，并记录相关实验指标。

## 功能概述

1. **数据处理流程**：
   - 读取`experiment`文件夹中的所有`exp_env_*.json`文件
   - 对每个JSON文件，使用`drone_mission_analyzer.py`进行分析
   - 将分析结果保存到对应文件夹的`task_analysis`子目录中
   - 基于分析结果，使用`drone_mission_generator.py`生成任务计划
   - 将生成的任务计划保存到`QGC_plan`目录中

2. **实验数据记录功能**：
   - 记录每个JSON文件中包含的多个用户需求(user_requirement)
   - 测量并记录LLM为每个需求生成规划所需的时间
   - 提供接口记录对应的人工规划时间，以便进行对比分析
   - 设计数据结构存储专家对每个任务规划的评分(1-10分)

3. **输出格式**：
   - JSON格式的详细数据记录
   - CSV格式的实验摘要，便于数据分析

## 系统组件

系统由以下三个主要模块组成：

1. **experiment_processor.py**：实验数据处理模块，负责调用分析器和生成器处理实验数据
2. **experiment_recorder.py**：实验数据记录模块，负责记录和导出实验数据
3. **experiment_manager.py**：实验管理器，整合处理和记录功能，提供命令行接口

## 使用方法

### 处理所有实验

```bash
python experiment_manager.py
```

### 处理单个实验

```bash
python experiment_manager.py --env_id 1
```

### 记录人工规划时间

```bash
python experiment_manager.py --record_manual_time
```

### 记录专家评分

```bash
python experiment_manager.py --record_expert_score
```

### 导出数据

```bash
python experiment_manager.py --export json
python experiment_manager.py --export csv
```

### 显示实验摘要

```bash
python experiment_manager.py --summary
```

### 指定LLM参数

```bash
python experiment_manager.py --api_key YOUR_API_KEY --model MODEL_NAME --api_url API_URL
```

## 数据结构

### 输入数据

系统处理的输入数据为`experiment/env_*/exp_env_*.json`文件，这些文件包含以下主要内容：

- `mission_prior`：任务先验信息，包括起飞点、飞行限制区域等
- `user_requirements`：用户需求列表，每个需求包含任务ID和描述

### 输出数据

系统生成的输出数据包括：

1. **任务分析结果**：保存在`experiment/env_*/task_analysis/task_*.json`文件中
2. **任务计划**：保存在`experiment/env_*/QGC_plan/*.plan`文件中
3. **实验记录**：保存在`experiment_results/experiment_record_*.json`文件中
4. **实验摘要**：保存在`experiment_results/experiment_summary_*.csv`文件中

## 示例

### 处理实验并记录数据

```python
from experiment_manager import ExperimentManager

# 创建实验管理器
manager = ExperimentManager()

# 处理所有实验
results = manager.process_experiments()

# 记录人工规划时间
manager.record_manual_planning_time("1", "1", 120.5)

# 记录专家评分
manager.record_expert_score("1", "1", 8)

# 获取实验摘要
summary = manager.get_experiment_summary()
print(summary)

# 导出数据
json_path = manager.export_data("json")
csv_path = manager.export_data("csv")
```

## 注意事项

1. 确保已安装所有依赖库
2. 确保`experiment`目录结构正确
3. 如果需要使用自定义的LLM API，请提供相应的API密钥和URL
