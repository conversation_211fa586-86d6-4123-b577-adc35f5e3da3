{"mission_prior": {"home_position": [37.5487879946958, 121.89097166061403, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[37.5530323239566, 121.88569307327272], [37.55422303451806, 121.8973231315613], [37.54427107996787, 121.89880371093751], [37.543165422804876, 121.88652992248535]]}, "target_areas": [{"id": "area_1", "name": "main_dock", "type": "port_infrastructure", "geometry": {"type": "polygon", "coordinates": [[37.550432, 121.890754], [37.550432, 121.892842], [37.549327, 121.892842], [37.549327, 121.890754]]}, "properties": {}}, {"id": "area_2", "name": "container_yard", "type": "storage", "geometry": {"type": "polygon", "coordinates": [[37.54981722618842, 121.8873453140259], [37.549764, 121.890582], [37.548660293701936, 121.89059615135194], [37.548719811937445, 121.88732385635377]]}, "properties": {}}, {"id": "area_3", "name": "ship_berthing_area", "type": "water", "geometry": {"type": "polygon", "coordinates": [[37.551243, 121.891687], [37.551243, 121.893426], [37.550128, 121.893426], [37.550128, 121.891687]]}, "properties": {}}, {"id": "area_4", "name": "port_warehouse", "type": "building", "geometry": {"type": "polygon", "coordinates": [[37.54857542594578, 121.88893318176271], [37.548549918087, 121.89013481140138], [37.547894864238074, 121.89011335372926], [37.547932, 121.888954]]}, "properties": {"height": 12}}, {"id": "area_5", "name": "port_office", "type": "building", "geometry": {"type": "polygon", "coordinates": [[37.548327, 121.891284], [37.548327, 121.891824], [37.547843, 121.891824], [37.547843, 121.891284]]}, "properties": {"height": 18}}, {"id": "area_6", "name": "loading_area", "type": "logistics", "geometry": {"type": "polygon", "coordinates": [[37.55031863665277, 121.89112186431886], [37.55031863665277, 121.89188361167909], [37.54985077536152, 121.89190506935121], [37.54986778030488, 121.89114332199098]]}, "properties": {}}, {"id": "area_7", "name": "transportation_corridor", "type": "road", "geometry": {"type": "linestring", "coordinates": [[37.550097933363624, 121.89090728759767], [37.55008056853236, 121.8893301486969], [37.549153443361554, 121.88930869102478], [37.5480903899894, 121.88963055610658], [37.546857315761365, 121.8920660018921]]}, "properties": {"width": 15}}, {"id": "area_8", "name": "coastline", "type": "natural", "geometry": {"type": "linestring", "coordinates": [[37.55196061851498, 121.88730239868165], [37.55223260305856, 121.89090728759767], [37.551697045258194, 121.89455509185791], [37.54815021103771, 121.8963146209717], [37.54674689788139, 121.89577817916872], [37.54483257462967, 121.88917994499208]]}, "properties": {}}, {"id": "area_9", "name": "breakwater", "type": "port_infrastructure", "geometry": {"type": "linestring", "coordinates": [[37.545566874600176, 121.89579963684083], [37.54838129446785, 121.89658284187318], [37.552173469154226, 121.89452290534975], [37.552576, 121.891824]]}, "properties": {"height": 5, "width": 10}}, {"id": "area_10", "name": "marina", "type": "port_infrastructure", "geometry": {"type": "polygon", "coordinates": [[37.55099802803466, 121.88745260238649], [37.55099802803466, 121.88816070556642], [37.55058109897648, 121.88820362091066], [37.550564094195835, 121.88748478889467]]}, "properties": {}}, {"id": "area_11", "name": "beach_area", "type": "natural", "geometry": {"type": "polygon", "coordinates": [[37.54568337463459, 121.88899755477907], [37.546567852363964, 121.89292430877687], [37.54618507927676, 121.89305305480958], [37.5452750664285, 121.88926577568056]]}, "properties": {}}, {"id": "area_12", "name": "coastal_road", "type": "road", "geometry": {"type": "linestring", "coordinates": [[37.546194269689046, 121.88733458518983], [37.54638965951743, 121.88946962356569], [37.54661910700106, 121.89093947410585], [37.54680606488761, 121.89270973205568], [37.547843279323054, 121.89417958259584]]}, "properties": {"width": 12}}, {"id": "area_13", "name": "customs_facility", "type": "building", "geometry": {"type": "polygon", "coordinates": [[37.547932, 121.891983], [37.547932, 121.892483], [37.547587, 121.892483], [37.547587, 121.891983]]}, "properties": {"height": 10}}, {"id": "area_14", "name": "oil_storage_tanks", "type": "industrial", "geometry": {"type": "polygon", "coordinates": [[37.54836259382703, 121.88777446746828], [37.54836259382703, 121.88857913017274], [37.54787775805827, 121.8885898590088], [37.54786925536007, 121.88780665397645]]}, "properties": {"height": 15}}]}, "user_requirements": [{"task_id": "1", "description": "Inspect the main dock for structural damage or signs of deterioration.", "description_cn": "检查主码头是否有结构损坏或老化迹象。"}, {"task_id": "2", "description": "Create a high-resolution map of the container yard with GSD of 3 cm/pixel for inventory management.", "description_cn": "创建集装箱堆场的高分辨率地图，地面采样距离为3厘米/像素，用于库存管理。"}, {"task_id": "3", "description": "Survey the coastline to document erosion patterns and identify areas requiring protection.", "description_cn": "测量海岸线，记录侵蚀模式并确定需要保护的区域。"}, {"task_id": "4", "description": "Inspect the breakwater structure for damage caused by recent storms.", "description_cn": "检查防波堤结构，确认是否因近期风暴造成损坏。"}, {"task_id": "5", "description": "Execute sequential navigation at 60m altitude around port facilities: [[37.550432, 121.891754], [37.549327, 121.892442], [37.548754, 121.889421], [37.547932, 121.889354]]", "description_cn": "以60米高度按顺序绕港口设施飞行。"}, {"task_id": "6", "description": "Perform a detailed mapping of the marina with 70% frontal overlap and 60% side overlap for planning renovations.", "description_cn": "以70%前向重叠和60%侧向重叠对游艇港进行详细测绘，用于规划翻新工作。"}, {"task_id": "7", "description": "Inspect the port warehouse roof and loading area for maintenance planning.", "description_cn": "检查港口仓库屋顶和装卸区，进行维护规划。"}, {"task_id": "8", "description": "Create an orthomosaic of the entire port area with a GSD of 8 cm/pixel for facility management.", "description_cn": "创建整个港口区域的正射影像图，地面采样距离为8厘米/像素，用于设施管理。"}, {"task_id": "9", "description": "Monitor ship berthing area for sediment buildup that might affect navigation.", "description_cn": "监测船舶泊位区域的泥沙堆积情况，评估是否会影响航行。"}, {"task_id": "10", "description": "Survey the beach area to assess coastal changes and tourism potential.", "description_cn": "测量海滩区域，评估海岸线变化和旅游潜力。"}, {"task_id": "11", "description": "Inspect the transportation corridor for surface damage that could impact logistics operations.", "description_cn": "检查运输通道的表面损坏情况，评估是否会影响物流运营。"}, {"task_id": "12", "description": "Conduct a security perimeter survey of the oil storage tanks and surrounding area.", "description_cn": "对石油储罐及周边区域进行安全周界勘察。"}, {"task_id": "13", "description": "Perform structural inspection of the port office building with focus on exterior elements.", "description_cn": "对港口办公楼进行结构检查，重点关注外部元素。"}, {"task_id": "14", "description": "Inspect the coastal road for erosion risks and maintenance needs.", "description_cn": "检查滨海公路，评估侵蚀风险和维护需求。"}, {"task_id": "15", "description": "Create highly detailed images of customs facility for security assessment.", "description_cn": "创建海关设施的高度详细图像，用于安全评估。"}]}