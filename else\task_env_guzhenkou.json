{"mission_prior": {"home_position": [35.769605209747354, 120.02861393193433, null], "flight_restricted_area": {}, "target_areas": [{"id": "area_1", "name": "图书馆", "type": "建筑物", "geometry": {"type": "polygon", "coordinates": [[35.76990676638383, 120.0299978882071], [35.76904973589437, 120.0299421116332], [35.76910913433394, 120.02887189859257], [35.76994636492583, 120.02892767519671]]}, "properties": {"height": 30}}, {"id": "area_2", "name": "古镇口-唐岛湾-海岸线", "type": "海岸线", "geometry": {"type": "linestring", "coordinates": [[35.76962678067685, 120.03388103076463], [35.7727923634999, 120.03322614442231], [35.77588134053773, 120.03254750584188], [35.778337568154264, 120.03413376647592], [35.77993166163038, 120.03638134958288], [35.78102362896363, 120.03923867047831], [35.78592774, 120.03605075], [35.79484452, 120.03593267], [35.80148342280991, 120.02815597084839], [35.80843715, 120.0238505], [35.812724106094215, 120.02437749571834], [35.83048256302162, 120.03051640190063], [35.8764962050974, 120.05801003154039], [35.886862791826225, 120.06650088993388], [35.89760717923498, 120.08931118737462], [35.91133699, 120.10999173], [35.938625843218695, 120.17712634219521]]}, "properties": {"width": 20}}, {"id": "area_3", "name": "中国石油大学古镇口校区", "type": "学校", "geometry": {"type": "polygon", "coordinates": [[35.76941292130731, 120.03155446889282], [35.76811535479048, 120.03090594410237], [35.76837247795451, 120.02279200812528], [35.7712306747459, 120.02338894505941], [35.770393556611815, 120.03117861863039]]}}, {"id": "area_4", "name": "灵山岛", "type": "岛屿", "geometry": {"type": "polygon", "coordinates": [[35.73897872285272, 120.16624966404169], [35.747945196565894, 120.15157031600052], [35.76200701475855, 120.15225131667466], [35.78478901308482, 120.16698857521999], [35.792775341940334, 120.17779481801182], [35.7558053837618, 120.18993335450756]]}}]}, "user_requirements": [{"task_id": "1", "description": "我想了解中国石油大学古镇口校区，帮我规划无人机飞行任务"}, {"task_id": "2", "description": "帮我完成对灵山岛的快速监测"}, {"task_id": "3", "description": "我想观察古镇口校区和唐岛湾校区，两个校区之间的海岸线"}, {"task_id": "4", "description": "帮我实现对图书馆的结构扫描，航向重叠度为70%，旁向重叠度为60%，GSD为0.9厘米/像素"}, {"task_id": "5", "description": "执行上面提供的环境中所有区域的无人机任务"}]}