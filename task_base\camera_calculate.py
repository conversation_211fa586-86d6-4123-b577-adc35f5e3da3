import json

class CameraCalc:
    def __init__(self):
        # 基本相机参数
        self.CameraName = "Sony ILCE-QX1"
        self.FocalLength = 16            # 焦距 (mm)
        self.SensorWidth = 23.2          # 传感器宽度 (mm)
        self.SensorHeight = 15.4         # 传感器高度 (mm)
        self.ImageWidth = 5456           # 图像宽度 (像素)
        self.ImageHeight = 3632          # 图像高度 (像素)
        
        # 其它参数
        self.DistanceToSurface = 30      # 地面距离 (米)
        self.ValueSetIsDistance = False   # True: 根据地面距离计算影像密度；False: 反向计算地面距离
        self.FrontalOverlap = 70         # 前向重叠率 (%)
        self.SideOverlap = 40            # 侧向重叠率 (%)
        self.Landscape = True            # True: 横向拍摄，False: 竖向拍摄
        
        # 固定参数（非手动相机时保存的信息）
        self.FixedOrientation = False
        self.MinTriggerInterval = 0
        self.DistanceMode = 1            # 距离模式（示例中用 1 表示一种模式）


        self.ImageDensity = 0.0

        self.AdjustedFootprintSide = 0.0
        self.AdjustedFootprintFrontal = 0.0

    def calculate_imaging_metrics(self):
        """
        根据参数计算影像密度、图像足迹及调整后的足迹。
        当 ValueSetIsDistance 为 True 时，使用地面距离计算影像密度；
        否则根据影像密度反向计算地面距离。
        """
        if self.ValueSetIsDistance:
            # 基于地面距离计算影像密度
            self.ImageDensity = (self.DistanceToSurface * self.SensorWidth * 100.0) / (self.ImageWidth * self.FocalLength)
        else:
            # 反向计算地面距离
            self.DistanceToSurface = (self.ImageWidth * self.ImageDensity * self.FocalLength) / (self.SensorWidth * 100.0)
        
        # 根据拍摄方向计算图像足迹（单位与 DistanceToSurface 保持一致，如米）
        if self.Landscape:
            imageFootprintSide = (self.ImageWidth * self.ImageDensity) / 100.0
            imageFootprintFrontal = (self.ImageHeight * self.ImageDensity) / 100.0
        else:
            imageFootprintSide = (self.ImageHeight * self.ImageDensity) / 100.0
            imageFootprintFrontal = (self.ImageWidth * self.ImageDensity) / 100.0

        # 根据重叠率调整足迹
        self.AdjustedFootprintSide = imageFootprintSide * ((100.0 - self.SideOverlap) / 100.0)
        self.AdjustedFootprintFrontal = imageFootprintFrontal * ((100.0 - self.FrontalOverlap) / 100.0)

    def save(self):
        data = {
            "AdjustedFootprintFrontal": self.AdjustedFootprintFrontal,
            "AdjustedFootprintSide": self.AdjustedFootprintSide,
            "CameraName": self.CameraName,
            "DistanceMode": self.DistanceMode,
            "DistanceToSurface": self.DistanceToSurface,
            "FixedOrientation": self.FixedOrientation,
            "FocalLength": self.FocalLength,
            "FrontalOverlap": self.FrontalOverlap,
            "ImageDensity": self.ImageDensity,
            "ImageHeight": self.ImageHeight,
            "ImageWidth": self.ImageWidth,
            "Landscape": self.Landscape,
            "MinTriggerInterval": self.MinTriggerInterval,
            "SensorHeight": self.SensorHeight,
            "SensorWidth": self.SensorWidth,
            "SideOverlap": self.SideOverlap,
            "ValueSetIsDistance": self.ValueSetIsDistance,
            "version": 2
        }
        return data

def generate_camera_param(ImageDensity,FrontalOverlap,SideOverlap):
    camera_calc = CameraCalc()
    camera_calc.ImageDensity = ImageDensity
    camera_calc.FrontalOverlap = FrontalOverlap         # 前向重叠率 (%)
    camera_calc.SideOverlap = SideOverlap            # 侧向重叠率 (%)
    camera_calc.calculate_imaging_metrics()

    data = camera_calc.save()
    return data
    

# 示例：执行计算并输出 JSON 状态
if __name__ == "__main__":
    camera_calc = generate_camera_param(1,75,40)
    print(json.dumps(camera_calc, indent=4))   
