import json
import os
from typing import List, Dict, Any
from corridor_scan import generate_corridor_scan_mission
from structure_scan import generate_structure_scan_mission
from survey import generate_survey_mission
from waypoints import generate_waypoints_mission
from camera_calculate import generate_camera_param


def check_required_params(params: Dict[str, Any], required_params: List[str], task_type: str, subtask_id: str) -> bool:
    """
    检查必要参数是否存在，如果不存在则打印错误信息。

    Args:
        params: 参数字典
        required_params: 必要参数列表
        task_type: 任务类型
        subtask_id: 子任务ID

    Returns:
        如果所有必要参数都存在，则返回True；否则返回False
    """
    missing_params = []
    for param in required_params:
        if param not in params:
            missing_params.append(param)

    if missing_params:
        print(f"错误: 子任务 {subtask_id} ({task_type}) 缺少必要参数: {', '.join(missing_params)}")
        print(f"需要的参数: {', '.join(required_params)}")
        print(f"提供的参数: {', '.join(params.keys())}")
        print(f"跳过此子任务。")
        return False

    return True


def check_all_subtasks_infeasible(subtasks: List[Dict[str, Any]]) -> bool:
    """
    检查是否所有子任务都不可行。

    Args:
        subtasks: 子任务列表

    Returns:
        如果所有子任务都不可行，则返回True；否则返回False
    """
    if not subtasks:
        return True  # 如果没有子任务，则认为所有子任务都不可行

    for subtask in subtasks:
        # 如果子任务没有feasible字段，则默认为可行
        if subtask.get('feasible', True):
            return False  # 找到至少一个可行的子任务

    return True  # 所有子任务都不可行

def generate_drone_missions(task_json_path: str) -> List[Dict[str, Any]]:
    """
    从task.json文件生成无人机任务。

    Args:
        task_json_path: task.json文件的路径

    Returns:
        任务项列表
    """
    # 读取task.json文件
    with open(task_json_path, 'r', encoding='utf-8') as f:
        task_data = json.load(f)

    # 获取子任务列表
    subtasks = task_data.get('subtasks', [])

    # 检查是否所有子任务都不可行
    if check_all_subtasks_infeasible(subtasks):
        print(f"警告: 所有子任务都不可行，无法生成任务。")
        if 'task_summary' in task_data:
            print(f"任务总结: {task_data['task_summary']}")
        elif 'feasibility_analysis' in task_data:
            print(f"可行性分析: {task_data['feasibility_analysis']}")
        return []

    # 初始化任务项列表和序列计数器
    mission_items = []
    current_seq_num = 2  # 起始序列号

    # 处理每个子任务
    for subtask in subtasks:
        # 检查子任务是否可行
        if 'feasible' in subtask and not subtask['feasible']:
            print(f"跳过不可行的子任务 {subtask.get('subtask_id', '未知')}: {subtask.get('feasibility_reason', '无原因')}")
            continue

        subtask_type = subtask.get('type', '')
        subtask_id = subtask.get('subtask_id', '未知')

        # 获取参数字典
        params = subtask.get('parameters', {})

        # 根据任务类型处理
        if subtask_type == "Survey task":
            # 检查必要参数
            required_params = ['gsd', 'frontal_overlap', 'lateral_overlap']
            if not check_required_params(params, required_params, subtask_type, subtask_id):
                continue  # 跳过此子任务

            # 提取多边形坐标
            geometry = subtask.get('geometry', {})
            if not geometry or geometry.get('type') != 'polygon' or not geometry.get('coordinates'):
                print(f"错误: 子任务 {subtask_id} ({subtask_type}) 缺少有效的多边形坐标")
                print(f"跳过此子任务。")
                continue

            coordinates = geometry.get('coordinates', [])

            # 检查坐标是否为空
            if not coordinates:
                print(f"错误: 子任务 {subtask_id} ({subtask_type}) 的坐标列表为空")
                print(f"跳过此子任务。")
                continue

            # 检查并处理坐标数据的不同嵌套层次
            # 只有当第一个元素是列表且其第一个元素也是列表时，才需要展平
            if isinstance(coordinates[0], list) and coordinates[0] and isinstance(coordinates[0][0], list):
                polygon = coordinates[0]
            else:
                polygon = coordinates

            # 校验每个坐标点是否包含两个值（纬度和经度）
            valid_coordinates = True
            for i, coord in enumerate(polygon):
                if not isinstance(coord, list) or len(coord) != 2:
                    print(f"错误: 子任务 {subtask_id} ({subtask_type}) 的坐标点 {i+1} 格式不正确: {coord}")
                    print("坐标点必须是包含两个值的列表 [纬度, 经度]")
                    valid_coordinates = False
                    break

            if not valid_coordinates:
                print(f"跳过此子任务。")
                continue

            # 提取参数 (已确保存在)
            gsd = params['gsd']
            frontal_overlap = params['frontal_overlap']
            lateral_overlap = params['lateral_overlap']

            try:
                # 生成相机参数
                camera_calc = generate_camera_param(gsd, frontal_overlap, lateral_overlap)

                # 生成测绘任务
                survey_items, item_count = generate_survey_mission(polygon, camera_calc, start_seq_num=current_seq_num)

                # 将项目添加到任务列表
                mission_items.append(survey_items)

                # 更新序列计数器
                current_seq_num += item_count

                print(f"成功生成子任务 {subtask_id} ({subtask_type}) 的任务项")
            except Exception as e:
                print(f"错误: 生成子任务 {subtask_id} ({subtask_type}) 的任务项时出错: {str(e)}")
                print(f"跳过此子任务。")
                continue

        elif subtask_type == "Corridor scan task":
            # 检查必要参数
            required_params = ['gsd', 'frontal_overlap', 'lateral_overlap', 'width']
            if not check_required_params(params, required_params, subtask_type, subtask_id):
                continue  # 跳过此子任务

            # 提取线串坐标
            geometry = subtask.get('geometry', {})
            if not geometry or geometry.get('type') != 'linestring' or not geometry.get('coordinates'):
                print(f"错误: 子任务 {subtask_id} ({subtask_type}) 缺少有效的坐标")
                print(f"跳过此子任务。")
                continue

            coordinates = geometry.get('coordinates', [])

            # 检查坐标是否为空
            if not coordinates:
                print(f"错误: 子任务 {subtask_id} ({subtask_type}) 的坐标列表为空")
                print(f"跳过此子任务。")
                continue

            # 检查并处理坐标数据的不同嵌套层次
            if isinstance(coordinates[0], list) and coordinates[0] and isinstance(coordinates[0][0], list):
                polyline = coordinates[0]
            else:
                polyline = coordinates

            # 校验每个坐标点是否包含两个值（纬度和经度）
            valid_coordinates = True
            for i, coord in enumerate(polyline):
                if not isinstance(coord, list) or len(coord) != 2:
                    print(f"错误: 子任务 {subtask_id} ({subtask_type}) 的坐标点 {i+1} 格式不正确: {coord}")
                    print("坐标点必须是包含两个值的列表 [纬度, 经度]")
                    valid_coordinates = False
                    break

            if not valid_coordinates:
                print(f"跳过此子任务。")
                continue

            # 提取参数 (已确保存在)
            gsd = params['gsd']
            frontal_overlap = params['frontal_overlap']
            lateral_overlap = params['lateral_overlap']
            corridor_width = params['width']

            try:
                # 生成相机参数
                camera_calc = generate_camera_param(gsd, frontal_overlap, lateral_overlap)

                # 生成走廊扫描任务
                corridor_items, item_count = generate_corridor_scan_mission(
                    polyline, camera_calc, corridor_width, start_seq_num=current_seq_num
                )

                # 将项目添加到任务列表
                mission_items.append(corridor_items)

                # 更新序列计数器
                current_seq_num += item_count

                print(f"成功生成子任务 {subtask_id} ({subtask_type}) 的任务项")
            except Exception as e:
                print(f"错误: 生成子任务 {subtask_id} ({subtask_type}) 的任务项时出错: {str(e)}")
                print(f"跳过此子任务。")
                continue

        elif subtask_type == "Structure scan task":
            # 检查必要参数
            required_params = ['gsd', 'frontal_overlap', 'lateral_overlap', 'height']
            if not check_required_params(params, required_params, subtask_type, subtask_id):
                continue  # 跳过此子任务

            # 提取多边形坐标
            geometry = subtask.get('geometry', {})
            if not geometry or geometry.get('type') != 'polygon' or not geometry.get('coordinates'):
                print(f"错误: 子任务 {subtask_id} ({subtask_type}) 缺少有效的多边形坐标")
                print(f"跳过此子任务。")
                continue

            coordinates = geometry.get('coordinates', [])

            # 检查坐标是否为空
            if not coordinates:
                print(f"错误: 子任务 {subtask_id} ({subtask_type}) 的坐标列表为空")
                print(f"跳过此子任务。")
                continue

            # 检查并处理坐标数据的不同嵌套层次
            if isinstance(coordinates[0], list) and coordinates[0] and isinstance(coordinates[0][0], list):
                polygon = coordinates[0]
            else:
                polygon = coordinates

            # 校验每个坐标点是否包含两个值（纬度和经度）
            valid_coordinates = True
            for i, coord in enumerate(polygon):
                if not isinstance(coord, list) or len(coord) != 2:
                    print(f"错误: 子任务 {subtask_id} ({subtask_type}) 的坐标点 {i+1} 格式不正确: {coord}")
                    print("坐标点必须是包含两个值的列表 [纬度, 经度]")
                    valid_coordinates = False
                    break

            if not valid_coordinates:
                print(f"跳过此子任务。")
                continue

            # 提取参数 (已确保存在)
            gsd = params['gsd']
            frontal_overlap = params['frontal_overlap']
            lateral_overlap = params['lateral_overlap']
            structure_height = params['height']

            try:
                # 生成相机参数
                camera_calc = generate_camera_param(gsd, frontal_overlap, lateral_overlap)

                # 生成结构扫描任务
                structure_items, item_count = generate_structure_scan_mission(
                    polygon, camera_calc, structure_height
                )

                # 将项目添加到任务列表
                mission_items.append(structure_items)

                # 更新序列计数器
                current_seq_num += item_count

                print(f"成功生成子任务 {subtask_id} ({subtask_type}) 的任务项")
            except Exception as e:
                print(f"错误: 生成子任务 {subtask_id} ({subtask_type}) 的任务项时出错: {str(e)}")
                print(f"跳过此子任务。")
                continue

        elif subtask_type == "Simple waypoint task":
            # 检查必要参数
            required_params = ['height']
            if not check_required_params(params, required_params, subtask_type, subtask_id):
                continue  # 跳过此子任务

            # 提取航点坐标
            geometry = subtask.get('geometry', {})
            if not geometry or geometry.get('type') != 'multipoint' or not geometry.get('coordinates'):
                print(f"错误: 子任务 {subtask_id} ({subtask_type}) 缺少有效的航点坐标")
                print(f"跳过此子任务。")
                continue

            coordinates = geometry.get('coordinates', [])

            # 检查坐标是否为空
            if not coordinates:
                print(f"错误: 子任务 {subtask_id} ({subtask_type}) 的坐标列表为空")
                print(f"跳过此子任务。")
                continue

            # 检查并处理坐标数据的不同嵌套层次
            if isinstance(coordinates[0], list) and coordinates[0] and isinstance(coordinates[0][0], list):
                waypoints = coordinates[0]
            else:
                waypoints = coordinates

            # 校验每个坐标点是否包含两个值（纬度和经度）
            valid_coordinates = True
            for i, coord in enumerate(waypoints):
                if not isinstance(coord, list) or len(coord) != 2:
                    print(f"错误: 子任务 {subtask_id} ({subtask_type}) 的坐标点 {i+1} 格式不正确: {coord}")
                    print("坐标点必须是包含两个值的列表 [纬度, 经度]")
                    valid_coordinates = False
                    break

            if not valid_coordinates:
                print(f"跳过此子任务。")
                continue

            # 提取参数 (已确保存在)
            fly_height = params['height']

            try:
                # 生成航点任务
                waypoint_items, item_count = generate_waypoints_mission(
                    waypoints, fly_height, start_seq_num=current_seq_num
                )

                # 将项目添加到任务列表
                mission_items.extend(waypoint_items)

                # 更新序列计数器
                current_seq_num += item_count

                print(f"成功生成子任务 {subtask_id} ({subtask_type}) 的任务项")
            except Exception as e:
                print(f"错误: 生成子任务 {subtask_id} ({subtask_type}) 的任务项时出错: {str(e)}")
                print(f"跳过此子任务。")
                continue
        else:
            print(f"警告: 未知的任务类型 '{subtask_type}' 在子任务 {subtask_id}")

    return mission_items

def build_complete_mission(task_data: Dict[str, Any], mission_items: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    构建完整的无人机任务文件，包括起飞项、返航项、地理围栏等。

    Args:
        task_data: 解析后的task.json数据
        mission_items: 生成的任务项列表

    Returns:
        完整的任务字典
    """
    # 获取home_position
    home_position = task_data.get('home_position')

    # 确保home_position有三个值
    if len(home_position) < 3 or home_position[2] is None:
        home_position = [home_position[0], home_position[1], None]

    # 创建起飞项
    takeoff_height = 30  # 默认起飞高度为30米
    takeoff_item = {
        "AMSLAltAboveTerrain": None,
        "Altitude": takeoff_height,
        "AltitudeMode": 1,
        "autoContinue": True,
        "command": 22,  # MAV_CMD_NAV_TAKEOFF
        "doJumpId": 1,  # 起飞项的doJumpId为1
        "frame": 3,
        "params": [
            0,
            0,
            0,
            None,
            home_position[0],  # 纬度
            home_position[1],  # 经度
            takeoff_height    # 高度
        ],
        "type": "SimpleItem"
    }

    # 处理mission_items，为它们分配正确的doJumpId
    processed_items = []
    next_jump_id = 2

    # 展平任务项
    flat_items = []
    for item in mission_items:
        if isinstance(item, dict):
            if "type" in item and item["type"] == "SimpleItem":
                flat_items.append(item)
            else:
                # 可能是ComplexItem，直接添加
                flat_items.append(item)
        elif isinstance(item, list):
            # 将列表中的每一项添加到展平的列表中
            for subitem in item:
                if isinstance(subitem, dict):
                    flat_items.append(subitem)

    # 更新doJumpId
    for item in flat_items:
        if isinstance(item, dict) and "type" in item and item["type"] == "SimpleItem":
            item["doJumpId"] = next_jump_id
            next_jump_id += 1
            processed_items.append(item)
        else:
            # 其他类型的项，直接添加
            processed_items.append(item)

    # 创建返航项
    return_item = {
        "autoContinue": True,
        "command": 20,  # MAV_CMD_NAV_RETURN_TO_LAUNCH
        "doJumpId": next_jump_id,
        "frame": 2,
        "params": [
            0, 0, 0, 0, 0, 0, 0
        ],
        "type": "SimpleItem"
    }

    # 组合所有任务项
    all_items = [takeoff_item] + processed_items + [return_item]

    # 根据flight_restricted_area创建地理围栏
    geofence = {
        "circles": [],
        "polygons": [],
        "version": 2
    }

    flight_restricted_area = task_data.get('flight_restricted_area')
    if flight_restricted_area and flight_restricted_area.get('type') == 'polygon':
        coordinates = flight_restricted_area.get('coordinates', [])
        geofence["polygons"].append({
            "inclusion": True,  # 这是一个包含区域（在此区域内飞行）
            "polygon": coordinates,
            "version": 1
        })

    # 构建完整任务
    complete_mission = {
        "fileType": "Plan",
        "geoFence": geofence,
        "groundStation": "QGroundControl",
        "mission": {
            "cruiseSpeed": 15,
            "firmwareType": 12,
            "globalPlanAltitudeMode": 1,
            "hoverSpeed": 5,
            "items": all_items,
            "plannedHomePosition": home_position,
            "vehicleType": 2,  # 多旋翼飞行器
            "version": 2
        },
        "rallyPoints": {
            "points": [],
            "version": 2
        },
        "version": 1
    }

    return complete_mission

def generate_mission(task_json_path="experiment/env_3/task_analysis"):
    """
    运行无人机任务生成器的主函数。

    Args:
        task_json_path: task.json文件的路径或目录路径，默认为"task_analysis/task_2.json"。
                      如果是目录路径，将处理该目录下所有的task_*.json文件。
    """

    try:
        # 检查路径是否存在
        if not os.path.exists(task_json_path):
            print(f"错误：路径 '{task_json_path}' 不存在！")
            return

        # 确定要处理的文件列表
        task_files = []

        if os.path.isdir(task_json_path):
            # 如果是目录，获取所有task_*.json文件
            for file in os.listdir(task_json_path):
                if file.startswith("task_") and file.endswith(".json") and "task_" in file:
                    task_files.append(os.path.join(task_json_path, file))

            # 按照任务编号排序
            task_files.sort(key=lambda x: int(os.path.basename(x).split("_")[1].split(".")[0]))

            if not task_files:
                print(f"警告：在目录 '{task_json_path}' 中未找到任何task_*.json文件")
                return
        else:
            # 如果是文件，直接添加到列表
            task_files.append(task_json_path)

        # 处理每个任务文件
        for task_file in task_files:
            # 读取task.json文件
            with open(task_file, 'r', encoding='utf-8') as f:
                task_data = json.load(f)

            # 显示任务描述
            task_id = task_data.get("task_id", "未知")
            description = task_data.get("description", "无描述")
            print(f"\n===== 处理任务 {task_id}: {description} =====")

            # 生成无人机任务
            print(f"正在为任务 {task_id} 生成无人机任务...")
            mission_items = generate_drone_missions(task_file)

            if not mission_items:
                # 读取任务文件以获取更多信息
                with open(task_file, 'r', encoding='utf-8') as f:
                    task_data = json.load(f)

                # 检查是否所有子任务都不可行
                if check_all_subtasks_infeasible(task_data.get('subtasks', [])):
                    print(f"信息：任务 {task_id} 中的所有子任务都不可行，无法生成任务。")

                    # 显示任务总结或可行性分析（如果有）
                    if 'task_summary' in task_data:
                        print(f"任务总结: {task_data['task_summary']}")
                    elif 'feasibility_analysis' in task_data:
                        print(f"可行性分析: {task_data['feasibility_analysis']}")
                else:
                    print(f"警告：未为任务 {task_id} 生成任何任务项，请检查任务文件格式是否正确。")

                continue

            # 构建完整任务
            print(f"正在为任务 {task_id} 构建完整任务文件...")
            complete_mission = build_complete_mission(task_data, mission_items)

            # 将完整任务保存到文件，使用description作为文件名
            task_dir = os.path.dirname(task_file)

            # 处理description作为文件名，移除不合法的字符
            safe_description = ""
            if description:
                # 限制文件名长度
                short_desc = description[:30]

                # 移除Windows文件名中不允许的字符
                invalid_chars = ['<', '>', ':', '"', '/', '\\', '|', '?', '*']
                for char in invalid_chars:
                    short_desc = short_desc.replace(char, '')

                # 替换空格
                safe_description = short_desc.replace(' ', '_')

                if safe_description:
                    safe_description = f"_{safe_description}"

            complete_mission_path = os.path.join(task_dir, f"{task_id}{safe_description}.plan")
            with open(complete_mission_path, 'w', encoding='utf-8') as f:
                json.dump(complete_mission, f, indent=2, ensure_ascii=False)

            print(f"已成功为任务 {task_id} 生成任务项和完整任务文件！")
            print(f"完整任务文件已保存到：{complete_mission_path}")

            # 显示任务统计信息
            try:
                # 统计mission_items类型
                mission_types = {}
                for item in mission_items:
                    if isinstance(item, dict):
                        item_type = item.get("type", "Unknown")
                    else:
                        item_type = f"类型: {type(item).__name__}"
                    mission_types[item_type] = mission_types.get(item_type, 0) + 1

                print(f"\n任务 {task_id} 项统计:")
                for item_type, count in mission_types.items():
                    print(f"  - {item_type}: {count}项")

                # 显示完整任务信息
                items_count = len(complete_mission["mission"]["items"])
                print(f"\n任务 {task_id} 完整任务信息:")
                print(f"  - 总任务项数量: {items_count}项")
                print(f"  - 起飞位置: 纬度 {complete_mission['mission']['plannedHomePosition'][0]}, "
                      f"经度 {complete_mission['mission']['plannedHomePosition'][1]}")

                if complete_mission["geoFence"]["polygons"]:
                    vertices = len(complete_mission["geoFence"]["polygons"][0]["polygon"])
                    print(f"  - 地理围栏: {vertices}个顶点的多边形")

            except Exception as e:
                print(f"统计任务 {task_id} 信息时出错: {str(e)}")

    except Exception as e:
        print(f"发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    generate_mission()