// 全局变量
let map;
let geoData;
let layers = {};
let editableLayers = {};
let originalData = null;
let measureMode = false;
let measureControl = null;
let measureMarkers = [];
let activeAreaId = null;

// 区域类型对应的颜色
const typeColors = {
    'home_position': '#3388ff',
    'flight_restricted_area': '#ff3333',
    'building': '#33cc33',
    'greenspace': '#33ff99',
    'commercial': '#ff9900',
    'construction': '#cc9966',
    'road': '#999999',
    'water': '#3399ff',
    'transportation': '#9966ff',
    'public_space': '#ff99cc'
};

// 初始化地图
function initMap() {
    // 创建地图实例
    map = L.map('map').setView([31.228435, 121.475267], 16);

    // 添加底图图层
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    // 添加比例尺
    L.control.scale({
        imperial: false,
        metric: true
    }).addTo(map);

    // 加载地理数据
    loadGeoData();

    // 初始化测量工具
    initMeasureTool();

    // 添加事件监听器
    document.getElementById('save-changes').addEventListener('click', saveChanges);
    document.getElementById('reset-changes').addEventListener('click', resetChanges);
    document.getElementById('measure-distance').addEventListener('click', toggleMeasureMode);
}

// 加载地理数据
function loadGeoData() {
    fetch('/api/geo-data')
        .then(response => response.json())
        .then(data => {
            geoData = data;
            originalData = JSON.parse(JSON.stringify(data)); // 深拷贝原始数据
            renderGeoData();
        })
        .catch(error => {
            console.error('加载地理数据失败:', error);
            alert('加载地理数据失败，请刷新页面重试。');
        });
}

// 渲染地理数据
function renderGeoData() {
    // 清除现有图层
    clearLayers();

    // 创建图层组
    layers = {
        home_position: L.layerGroup(),
        flight_restricted_area: L.layerGroup(),
        target_areas: L.layerGroup()
    };

    // 渲染home_position
    renderHomePosition();

    // 渲染flight_restricted_area
    renderFlightRestrictedArea();

    // 渲染target_areas
    renderTargetAreas();

    // 将图层添加到地图
    for (const key in layers) {
        layers[key].addTo(map);
    }

    // 生成图层控制
    generateLayerControls();
}

// 渲染home_position
function renderHomePosition() {
    const homePos = geoData.mission_prior.home_position;
    if (homePos && homePos[0] && homePos[1]) {
        const marker = L.marker([homePos[0], homePos[1]], {
            draggable: true,
            title: 'Home Position',
            icon: L.divIcon({
                className: 'custom-div-icon',
                html: `<div style="background-color:${typeColors.home_position}; width:12px; height:12px; border-radius:50%; border:2px solid white;"></div>`,
                iconSize: [12, 12],
                iconAnchor: [6, 6]
            })
        });

        marker.on('dragend', function(event) {
            const position = marker.getLatLng();
            geoData.mission_prior.home_position[0] = position.lat;
            geoData.mission_prior.home_position[1] = position.lng;
            updateCoordinateInfo('home_position', null, [position.lat, position.lng]);
        });

        marker.on('click', function() {
            updateCoordinateInfo('home_position', null, homePos);
        });

        layers.home_position.addLayer(marker);
        editableLayers.home_position = marker;
    }
}

// 渲染flight_restricted_area
function renderFlightRestrictedArea() {
    const restrictedArea = geoData.mission_prior.flight_restricted_area;
    if (restrictedArea && restrictedArea.coordinates && restrictedArea.coordinates.length > 0) {
        const coords = restrictedArea.coordinates.map(coord => [coord[0], coord[1]]);

        // 创建多边形
        const polygon = L.polygon(coords, {
            color: typeColors.flight_restricted_area,
            fillOpacity: 0.2
        });

        layers.flight_restricted_area.addLayer(polygon);

        // 为每个顶点创建可拖动的标记
        const markers = [];
        coords.forEach((coord, index) => {
            const marker = createDraggableMarker(
                coord,
                typeColors.flight_restricted_area,
                'flight_restricted_area',
                null,
                index
            );
            layers.flight_restricted_area.addLayer(marker);
            markers.push(marker);
        });

        editableLayers.flight_restricted_area = {
            polygon: polygon,
            markers: markers
        };
    }
}

// 渲染target_areas
function renderTargetAreas() {
    const targetAreas = geoData.mission_prior.target_areas;
    if (targetAreas && targetAreas.length > 0) {
        targetAreas.forEach((area, areaIndex) => {
            const areaId = area.id;
            const areaName = area.name;
            const areaType = area.type;
            const geometry = area.geometry;

            if (!geometry || !geometry.coordinates || geometry.coordinates.length === 0) {
                return;
            }

            const color = typeColors[areaType] || '#777777';

            if (geometry.type === 'polygon') {
                const coords = geometry.coordinates.map(coord => [coord[0], coord[1]]);

                // 创建多边形
                const polygon = L.polygon(coords, {
                    color: color,
                    fillOpacity: 0.2
                }).bindTooltip(areaName);

                layers.target_areas.addLayer(polygon);

                // 为每个顶点创建可拖动的标记
                const markers = [];
                coords.forEach((coord, index) => {
                    const marker = createDraggableMarker(
                        coord,
                        color,
                        'target_areas',
                        areaId,
                        index
                    );
                    layers.target_areas.addLayer(marker);
                    markers.push(marker);
                });

                if (!editableLayers.target_areas) {
                    editableLayers.target_areas = {};
                }

                editableLayers.target_areas[areaId] = {
                    polygon: polygon,
                    markers: markers,
                    type: 'polygon'
                };

            } else if (geometry.type === 'linestring') {
                const coords = geometry.coordinates.map(coord => [coord[0], coord[1]]);

                // 创建折线
                const polyline = L.polyline(coords, {
                    color: color,
                    weight: 3
                }).bindTooltip(areaName);

                layers.target_areas.addLayer(polyline);

                // 为每个顶点创建可拖动的标记
                const markers = [];
                coords.forEach((coord, index) => {
                    const marker = createDraggableMarker(
                        coord,
                        color,
                        'target_areas',
                        areaId,
                        index
                    );
                    layers.target_areas.addLayer(marker);
                    markers.push(marker);
                });

                if (!editableLayers.target_areas) {
                    editableLayers.target_areas = {};
                }

                editableLayers.target_areas[areaId] = {
                    polyline: polyline,
                    markers: markers,
                    type: 'linestring'
                };
            }
        });
    }
}

// 创建可拖动的标记
function createDraggableMarker(coord, color, layerType, areaId, index) {
    const marker = L.marker([coord[0], coord[1]], {
        draggable: true,
        icon: L.divIcon({
            className: 'custom-div-icon',
            html: `<div style="background-color:${color}; width:8px; height:8px; border-radius:50%; border:2px solid white;"></div>`,
            iconSize: [8, 8],
            iconAnchor: [4, 4]
        })
    });

    marker.on('dragstart', function() {
        if (areaId) {
            highlightArea(areaId);
        }
    });

    marker.on('drag', function(event) {
        updateGeometryOnDrag(layerType, areaId, index, marker.getLatLng());
    });

    marker.on('dragend', function(event) {
        updateGeometryOnDrag(layerType, areaId, index, marker.getLatLng());
        if (areaId) {
            updateCoordinateInfo(layerType, areaId, [marker.getLatLng().lat, marker.getLatLng().lng], index);
            // 拖动结束后重置高亮状态，确保点仍然可以被选中
            resetHighlightState();
        } else {
            updateCoordinateInfo(layerType, null, [marker.getLatLng().lat, marker.getLatLng().lng], index);
        }
    });

    marker.on('click', function() {
        if (measureMode) {
            handleMeasureClick([coord[0], coord[1]]);
        } else {
            // 如果当前点击的是已经高亮的区域中的点，先重置高亮状态
            if (areaId && activeAreaId === areaId) {
                resetHighlightState();
            }

            if (areaId) {
                // 高亮显示当前区域
                highlightArea(areaId);
                const area = geoData.mission_prior.target_areas.find(a => a.id === areaId);
                updateCoordinateInfo(layerType, areaId, area.geometry.coordinates[index], index);
            } else if (layerType === 'flight_restricted_area') {
                // 清除所有高亮效果
                resetHighlightState();
                updateCoordinateInfo(layerType, null, geoData.mission_prior.flight_restricted_area.coordinates[index], index);
            }
        }
    });

    return marker;
}

// 在拖动时更新几何图形
function updateGeometryOnDrag(layerType, areaId, index, latLng) {
    if (layerType === 'home_position') {
        geoData.mission_prior.home_position[0] = latLng.lat;
        geoData.mission_prior.home_position[1] = latLng.lng;
    } else if (layerType === 'flight_restricted_area') {
        geoData.mission_prior.flight_restricted_area.coordinates[index][0] = latLng.lat;
        geoData.mission_prior.flight_restricted_area.coordinates[index][1] = latLng.lng;

        // 更新多边形
        const newCoords = geoData.mission_prior.flight_restricted_area.coordinates.map(coord => [coord[0], coord[1]]);
        editableLayers.flight_restricted_area.polygon.setLatLngs(newCoords);
    } else if (layerType === 'target_areas' && areaId) {
        const areaIndex = geoData.mission_prior.target_areas.findIndex(area => area.id === areaId);
        if (areaIndex !== -1) {
            geoData.mission_prior.target_areas[areaIndex].geometry.coordinates[index][0] = latLng.lat;
            geoData.mission_prior.target_areas[areaIndex].geometry.coordinates[index][1] = latLng.lng;

            // 更新多边形或折线
            const newCoords = geoData.mission_prior.target_areas[areaIndex].geometry.coordinates.map(coord => [coord[0], coord[1]]);
            if (editableLayers.target_areas[areaId].type === 'polygon') {
                editableLayers.target_areas[areaId].polygon.setLatLngs(newCoords);
            } else if (editableLayers.target_areas[areaId].type === 'linestring') {
                editableLayers.target_areas[areaId].polyline.setLatLngs(newCoords);
            }
        }
    }
}

// 高亮显示区域
function highlightArea(areaId) {
    // 设置当前活动区域ID
    activeAreaId = areaId;

    // 移除所有高亮效果
    for (const id in editableLayers.target_areas) {
        const layer = editableLayers.target_areas[id];
        layer.markers.forEach(marker => {
            marker.getElement().classList.remove('highlighted-point');
        });
    }

    // 为当前区域添加高亮效果
    if (editableLayers.target_areas[areaId]) {
        editableLayers.target_areas[areaId].markers.forEach(marker => {
            marker.getElement().classList.add('highlighted-point');
        });
    }
}

// 重置高亮状态
function resetHighlightState() {
    // 清除当前活动区域ID
    activeAreaId = null;

    // 移除所有高亮效果
    for (const id in editableLayers.target_areas) {
        const layer = editableLayers.target_areas[id];
        layer.markers.forEach(marker => {
            marker.getElement().classList.remove('highlighted-point');
        });
    }
}

// 更新坐标信息显示
function updateCoordinateInfo(layerType, areaId, coordinates, index) {
    const infoBox = document.getElementById('coordinate-info');
    let html = '';

    if (layerType === 'home_position') {
        html = `
            <h4>Home Position</h4>
            <div class="coordinate-form">
                <label>纬度:</label>
                <input type="number" step="0.000001" id="home-lat" value="${coordinates[0]}" onchange="updateCoordinate('home_position', null, 0, this.value)">
                <label>经度:</label>
                <input type="number" step="0.000001" id="home-lng" value="${coordinates[1]}" onchange="updateCoordinate('home_position', null, 1, this.value)">
            </div>
        `;
    } else if (layerType === 'flight_restricted_area') {
        html = `
            <h4>Flight Restricted Area - 点 ${index + 1}</h4>
            <div class="coordinate-form">
                <label>纬度:</label>
                <input type="number" step="0.000001" id="restricted-lat-${index}" value="${coordinates[0]}" onchange="updateCoordinate('flight_restricted_area', null, ${index}, 0, this.value)">
                <label>经度:</label>
                <input type="number" step="0.000001" id="restricted-lng-${index}" value="${coordinates[1]}" onchange="updateCoordinate('flight_restricted_area', null, ${index}, 1, this.value)">
            </div>
        `;
    } else if (layerType === 'target_areas' && areaId) {
        const area = geoData.mission_prior.target_areas.find(a => a.id === areaId);
        html = `
            <h4>${area.name} (${area.type}) - 点 ${index + 1}</h4>
            <div class="coordinate-form">
                <label>纬度:</label>
                <input type="number" step="0.000001" id="target-lat-${areaId}-${index}" value="${coordinates[0]}" onchange="updateCoordinate('target_areas', '${areaId}', ${index}, 0, this.value)">
                <label>经度:</label>
                <input type="number" step="0.000001" id="target-lng-${areaId}-${index}" value="${coordinates[1]}" onchange="updateCoordinate('target_areas', '${areaId}', ${index}, 1, this.value)">
            </div>
        `;
    }

    infoBox.innerHTML = html;
}

// 通过输入框更新坐标
function updateCoordinate(layerType, areaId, index, coordIndex, value) {
    if (arguments.length === 4) {
        // 处理home_position的特殊情况
        value = coordIndex;
        coordIndex = index;
        index = null;
    }

    const numValue = parseFloat(value);

    if (isNaN(numValue)) {
        alert('请输入有效的数字');
        return;
    }

    if (layerType === 'home_position') {
        geoData.mission_prior.home_position[coordIndex] = numValue;
        editableLayers.home_position.setLatLng([
            geoData.mission_prior.home_position[0],
            geoData.mission_prior.home_position[1]
        ]);
    } else if (layerType === 'flight_restricted_area') {
        geoData.mission_prior.flight_restricted_area.coordinates[index][coordIndex] = numValue;

        // 更新标记位置
        const marker = editableLayers.flight_restricted_area.markers[index];
        const latLng = marker.getLatLng();
        if (coordIndex === 0) {
            marker.setLatLng([numValue, latLng.lng]);
        } else {
            marker.setLatLng([latLng.lat, numValue]);
        }

        // 更新多边形
        const newCoords = geoData.mission_prior.flight_restricted_area.coordinates.map(coord => [coord[0], coord[1]]);
        editableLayers.flight_restricted_area.polygon.setLatLngs(newCoords);
    } else if (layerType === 'target_areas' && areaId) {
        const areaIndex = geoData.mission_prior.target_areas.findIndex(area => area.id === areaId);
        if (areaIndex !== -1) {
            geoData.mission_prior.target_areas[areaIndex].geometry.coordinates[index][coordIndex] = numValue;

            // 更新标记位置
            const marker = editableLayers.target_areas[areaId].markers[index];
            const latLng = marker.getLatLng();
            if (coordIndex === 0) {
                marker.setLatLng([numValue, latLng.lng]);
            } else {
                marker.setLatLng([latLng.lat, numValue]);
            }

            // 更新多边形或折线
            const newCoords = geoData.mission_prior.target_areas[areaIndex].geometry.coordinates.map(coord => [coord[0], coord[1]]);
            if (editableLayers.target_areas[areaId].type === 'polygon') {
                editableLayers.target_areas[areaId].polygon.setLatLngs(newCoords);
            } else if (editableLayers.target_areas[areaId].type === 'linestring') {
                editableLayers.target_areas[areaId].polyline.setLatLngs(newCoords);
            }

            // 通过输入框更新坐标后重置高亮状态
            resetHighlightState();
        }
    }
}

// 生成图层控制
function generateLayerControls() {
    const layerControlsDiv = document.getElementById('layer-controls');
    let html = '';

    // Home Position 图层
    html += `
        <div class="layer-control">
            <input type="checkbox" id="layer-home" checked onchange="toggleLayer('home_position', this.checked)">
            <label for="layer-home">Home Position</label>
        </div>
    `;

    // Flight Restricted Area 图层
    html += `
        <div class="layer-control">
            <input type="checkbox" id="layer-restricted" checked onchange="toggleLayer('flight_restricted_area', this.checked)">
            <label for="layer-restricted">Flight Restricted Area</label>
        </div>
    `;

    // Target Areas 图层
    const targetAreas = geoData.mission_prior.target_areas;
    targetAreas.forEach(area => {
        html += `
            <div class="layer-control">
                <input type="checkbox" id="layer-${area.id}" checked onchange="toggleTargetArea('${area.id}', this.checked)">
                <label for="layer-${area.id}">${area.name}</label>
            </div>
        `;
    });

    layerControlsDiv.innerHTML = html;
}

// 切换图层显示
function toggleLayer(layerName, visible) {
    if (visible) {
        map.addLayer(layers[layerName]);
    } else {
        map.removeLayer(layers[layerName]);
    }
}

// 切换目标区域显示
function toggleTargetArea(areaId, visible) {
    if (!editableLayers.target_areas || !editableLayers.target_areas[areaId]) {
        return;
    }

    const layer = editableLayers.target_areas[areaId];

    if (visible) {
        // 显示多边形或折线
        if (layer.type === 'polygon') {
            layers.target_areas.addLayer(layer.polygon);
        } else if (layer.type === 'linestring') {
            layers.target_areas.addLayer(layer.polyline);
        }

        // 显示标记
        layer.markers.forEach(marker => {
            layers.target_areas.addLayer(marker);
        });
    } else {
        // 隐藏多边形或折线
        if (layer.type === 'polygon') {
            layers.target_areas.removeLayer(layer.polygon);
        } else if (layer.type === 'linestring') {
            layers.target_areas.removeLayer(layer.polyline);
        }

        // 隐藏标记
        layer.markers.forEach(marker => {
            layers.target_areas.removeLayer(marker);
        });
    }
}

// 清除所有图层
function clearLayers() {
    if (map) {
        for (const key in layers) {
            if (map.hasLayer(layers[key])) {
                map.removeLayer(layers[key]);
            }
        }
    }

    layers = {};
    editableLayers = {};
}

// 保存更改
function saveChanges() {
    const dataToSave = {
        home_position: geoData.mission_prior.home_position,
        flight_restricted_area: geoData.mission_prior.flight_restricted_area.coordinates,
        target_areas: geoData.mission_prior.target_areas.map(area => ({
            id: area.id,
            coordinates: area.geometry.coordinates
        }))
    };

    fetch('/api/update-coordinates', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(dataToSave)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('坐标数据已成功保存！');
            // 更新原始数据
            originalData = JSON.parse(JSON.stringify(geoData));
        } else {
            alert('保存失败: ' + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        console.error('保存数据失败:', error);
        alert('保存数据失败，请重试。');
    });
}

// 重置更改
function resetChanges() {
    if (confirm('确定要重置所有更改吗？这将恢复到上次保存的状态。')) {
        geoData = JSON.parse(JSON.stringify(originalData));
        renderGeoData();
    }
}

// 初始化测量工具
function initMeasureTool() {
    // 创建测量结果显示区域
    const measurementResult = document.getElementById('measurement-result');
    measurementResult.innerHTML = '点击"测量距离"按钮开始测量';
}

// 切换测量模式
function toggleMeasureMode() {
    measureMode = !measureMode;

    const measureButton = document.getElementById('measure-distance');
    const measurementResult = document.getElementById('measurement-result');

    if (measureMode) {
        measureButton.textContent = '取消测量';
        measureButton.classList.add('primary');
        measurementResult.innerHTML = '请在地图上点击两个点来测量距离';

        // 清除之前的测量标记
        clearMeasureMarkers();
    } else {
        measureButton.textContent = '测量距离';
        measureButton.classList.remove('primary');
        measurementResult.innerHTML = '点击"测量距离"按钮开始测量';

        // 清除测量标记
        clearMeasureMarkers();
    }
}

// 处理测量点击
function handleMeasureClick(coord) {
    if (!measureMode) return;

    // 添加测量点标记
    const marker = L.marker([coord[0], coord[1]], {
        icon: L.divIcon({
            className: 'measure-point',
            html: `<div style="background-color:#ff00ff; width:8px; height:8px; border-radius:50%; border:2px solid white;"></div>`,
            iconSize: [8, 8],
            iconAnchor: [4, 4]
        })
    }).addTo(map);

    measureMarkers.push(marker);

    // 如果有两个点，计算距离
    if (measureMarkers.length === 2) {
        const point1 = [measureMarkers[0].getLatLng().lat, measureMarkers[0].getLatLng().lng];
        const point2 = [measureMarkers[1].getLatLng().lat, measureMarkers[1].getLatLng().lng];

        // 绘制连线
        const line = L.polyline([point1, point2], {
            color: '#ff00ff',
            dashArray: '5, 5',
            weight: 2
        }).addTo(map);

        measureMarkers.push(line);

        // 计算距离
        calculateDistance(point1, point2);
    } else if (measureMarkers.length > 2) {
        // 重置测量
        clearMeasureMarkers();

        // 添加新的第一个点
        const marker = L.marker([coord[0], coord[1]], {
            icon: L.divIcon({
                className: 'measure-point',
                html: `<div style="background-color:#ff00ff; width:8px; height:8px; border-radius:50%; border:2px solid white;"></div>`,
                iconSize: [8, 8],
                iconAnchor: [4, 4]
            })
        }).addTo(map);

        measureMarkers.push(marker);

        const measurementResult = document.getElementById('measurement-result');
        measurementResult.innerHTML = '请点击第二个点来完成测量';
    }
}

// 计算两点之间的距离
function calculateDistance(point1, point2) {
    fetch('/api/measure-distance', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ point1, point2 })
    })
    .then(response => response.json())
    .then(data => {
        const distance = data.distance;
        const measurementResult = document.getElementById('measurement-result');

        if (distance < 1000) {
            measurementResult.innerHTML = `距离: ${distance.toFixed(2)} 米`;
        } else {
            measurementResult.innerHTML = `距离: ${(distance / 1000).toFixed(2)} 公里`;
        }
    })
    .catch(error => {
        console.error('计算距离失败:', error);
        const measurementResult = document.getElementById('measurement-result');
        measurementResult.innerHTML = '计算距离失败，请重试';
    });
}

// 清除测量标记
function clearMeasureMarkers() {
    measureMarkers.forEach(marker => {
        map.removeLayer(marker);
    });
    measureMarkers = [];
}

// 页面加载完成后初始化地图
document.addEventListener('DOMContentLoaded', initMap);
