import math
import json
from enum import Enum, auto
from typing import List, Tuple, Dict, Optional


class CoordType(Enum):
    """坐标点类型枚举"""
    TURNAROUND = auto()               # 转弯点
    SURVEY_ENTRY = auto()             # 测量区域入口点
    SURVEY_EXIT = auto()              # 测量区域出口点
    INTERIOR_HOVER_TRIGGER = auto()   # 内部悬停触发点
    INTERIOR = auto()                 # 内部普通点（新增）

class EntryLocation(Enum):
    """入口位置枚举"""
    TOP_LEFT = 0
    TOP_RIGHT = 1
    BOTTOM_LEFT = 2
    BOTTOM_RIGHT = 3
    CENTER = 4
    FIRST = 5
    LAST = 6

class MavFrame(Enum):
    """MAV坐标系常量"""
    GLOBAL_RELATIVE_ALT = 3
    MISSION = 2

class MavCmd(Enum):
    """MAV命令常量"""
    NAV_WAYPOINT = 16
    IMAGE_START_CAPTURE = 2000
    DO_SET_CAM_TRIGG_DIST = 206

class CameraCalc:
    """相机参数计算类"""
    def __init__(self):
        self.adjusted_footprint_frontal = 0.0
        self.adjusted_footprint_side = 0.0
        self.camera_name = ""
        self.distance_mode = 1
        self.distance_to_surface = 0.0
        self.fixed_orientation = False
        self.focal_length = 0.0
        self.frontal_overlap = 0
        self.image_density = 0
        self.image_height = 0
        self.image_width = 0
        self.landscape = True
        self.min_trigger_interval = 0.0
        self.sensor_height = 0.0
        self.sensor_width = 0.0
        self.side_overlap = 0
        self.value_set_is_distance = False
        self.version = 2
    
    def from_dict(self, camera_dict):
        """从字典中加载相机参数"""
        self.adjusted_footprint_frontal = camera_dict.get("AdjustedFootprintFrontal", 0.0)
        self.adjusted_footprint_side = camera_dict.get("AdjustedFootprintSide", 0.0)
        self.camera_name = camera_dict.get("CameraName", "")
        self.distance_mode = camera_dict.get("DistanceMode", 1)
        self.distance_to_surface = camera_dict.get("DistanceToSurface", 0.0)
        self.fixed_orientation = camera_dict.get("FixedOrientation", False)
        self.focal_length = camera_dict.get("FocalLength", 0.0)
        self.frontal_overlap = camera_dict.get("FrontalOverlap", 0)
        self.image_density = camera_dict.get("ImageDensity", 0)
        self.image_height = camera_dict.get("ImageHeight", 0)
        self.image_width = camera_dict.get("ImageWidth", 0)
        self.landscape = camera_dict.get("Landscape", True)
        self.min_trigger_interval = camera_dict.get("MinTriggerInterval", 0.0)
        self.sensor_height = camera_dict.get("SensorHeight", 0.0)
        self.sensor_width = camera_dict.get("SensorWidth", 0.0)
        self.side_overlap = camera_dict.get("SideOverlap", 0)
        self.value_set_is_distance = camera_dict.get("ValueSetIsDistance", False)
        self.version = camera_dict.get("version", 2)
        
    def to_dict(self):
        """转换为字典表示"""
        return {
            "AdjustedFootprintFrontal": self.adjusted_footprint_frontal,
            "AdjustedFootprintSide": self.adjusted_footprint_side,
            "CameraName": self.camera_name,
            "DistanceMode": self.distance_mode,
            "DistanceToSurface": self.distance_to_surface,
            "FixedOrientation": self.fixed_orientation,
            "FocalLength": self.focal_length,
            "FrontalOverlap": self.frontal_overlap,
            "ImageDensity": self.image_density,
            "ImageHeight": self.image_height,
            "ImageWidth": self.image_width,
            "Landscape": self.landscape,
            "MinTriggerInterval": self.min_trigger_interval,
            "SensorHeight": self.sensor_height,
            "SensorWidth": self.sensor_width,
            "SideOverlap": self.side_overlap,
            "ValueSetIsDistance": self.value_set_is_distance,
            "version": self.version
        }



class GeoCoordinate:
    """地理坐标类"""
    def __init__(self, latitude: float, longitude: float, altitude: float = None):
        self.latitude = latitude
        self.longitude = longitude
        self.altitude = altitude
    
    def distance_to(self, other: 'GeoCoordinate') -> float:
        """计算到另一个坐标的距离 (米)"""
        # 使用球面余弦定理计算距离
        lat1, lon1 = math.radians(self.latitude), math.radians(self.longitude)
        lat2, lon2 = math.radians(other.latitude), math.radians(other.longitude)
        
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))
        r = 6371000  # 地球半径 (米)
        return c * r
    
    def azimuth_to(self, other: 'GeoCoordinate') -> float:
        """计算到另一个坐标的方位角 (度)"""
        lat1, lon1 = math.radians(self.latitude), math.radians(self.longitude)
        lat2, lon2 = math.radians(other.latitude), math.radians(other.longitude)
        
        dlon = lon2 - lon1
        
        y = math.sin(dlon) * math.cos(lat2)
        x = math.cos(lat1) * math.sin(lat2) - math.sin(lat1) * math.cos(lat2) * math.cos(dlon)
        
        bearing = math.atan2(y, x)
        bearing = math.degrees(bearing)
        bearing = (bearing + 360) % 360
        
        return bearing
    
    def at_distance_and_azimuth(self, distance: float, azimuth: float) -> 'GeoCoordinate':
        """返回距离此点指定距离和方位角的新坐标"""
        lat1 = math.radians(self.latitude)
        lon1 = math.radians(self.longitude)
        brng = math.radians(azimuth)
        
        # 地球半径 (米)
        R = 6371000
        
        # 计算新的经纬度
        lat2 = math.asin(math.sin(lat1) * math.cos(distance/R) + 
                         math.cos(lat1) * math.sin(distance/R) * math.cos(brng))
        
        lon2 = lon1 + math.atan2(math.sin(brng) * math.sin(distance/R) * math.cos(lat1),
                               math.cos(distance/R) - math.sin(lat1) * math.sin(lat2))
        
        return GeoCoordinate(math.degrees(lat2), math.degrees(lon2), self.altitude)
    
    def __str__(self):
        return f"({self.latitude}, {self.longitude}, {self.altitude})"

class CoordInfo:
    """坐标信息类"""
    def __init__(self, coord: GeoCoordinate, coord_type: CoordType):
        self.coord = coord
        self.coord_type = coord_type

class MissionItem:
    """任务项类"""
    def __init__(self, seq_num, command, frame, param1, param2, param3, param4, 
                 latitude, longitude, altitude, auto_continue=True, is_current=False):
        self.seq_num = seq_num
        self.command = command
        self.frame = frame
        self.param1 = param1
        self.param2 = param2
        self.param3 = param3
        self.param4 = param4
        self.latitude = latitude
        self.longitude = longitude
        self.altitude = altitude
        self.auto_continue = auto_continue
        self.is_current = is_current
    
    def __str__(self):
        """格式化输出任务项信息"""
        return f"MissionItem({self.seq_num}, {self.command.name}, {self.frame.name}, {self.param1}, {self.param2}, " \
               f"{self.param3}, {self.param4}, {self.latitude}, {self.longitude}, {self.altitude})"

class SurveyComplexItem:
    """测绘任务规划类"""
    def __init__(self):
        # 输入参数
        self.survey_area_polygon: List[GeoCoordinate] = []
        self.camera_footprint_side = 0        # 相机侧向覆盖宽度 (米)
        self.camera_footprint_frontal = 0     # 相机前向覆盖宽度 (米)
        self.distance_to_surface = 0          # 飞行高度 (米)
        self.grid_angle = 0                   # 网格角度 (度)
        self.entry_point = EntryLocation.TOP_LEFT  # 入口点位置
        self.turn_around_distance = 10.0         # 转弯距离 (米)
        self.fly_alternate_transects = False  # 是否交错飞行
        self.refly_90_degrees = False         # 是否正交重飞
        self.hover_and_capture = False        # 是否悬停拍照
        self.hover_and_capture_delay = 0      # 悬停延迟 (秒)
        self.camera_trigger_in_turn_around = False  # 转弯处是否触发相机
        self.vehicle_speed = 5.0              # 飞行速度 (米/秒)
        self.camera_min_trigger_interval = 0  # 相机最小触发间隔 (秒)
        self.split_concave_polygons = True    # 是否拆分凹多边形
        
        # 计算结果
        self.transects: List[List[CoordInfo]] = []  # 生成的航线
        self.camera_shots = 0                 # 相机拍摄次数
        self.complex_distance = 0             # 飞行总距离
        
        # 任务项生成相关参数
        self.sequence_number = 1             # 任务项序列号起始值
        self.mission_items = []              # 生成的任务项列表
        
        # 相机参数
        self.camera_calc = CameraCalc()      # 相机参数计算对象
        
    def _geo_to_ned(self, coord: GeoCoordinate, tangent_origin: GeoCoordinate) -> Tuple[float, float]:
        """将地理坐标转换为 NED (北-东-下) 坐标"""
        if coord.latitude == tangent_origin.latitude and coord.longitude == tangent_origin.longitude:
            return 0, 0
            
        # 简化版转换，仅适用于小范围区域
        lat_scale = 111320  # 每度纬度约 111320 米
        lon_scale = 111320 * math.cos(math.radians(tangent_origin.latitude))  # 根据纬度调整经度刻度
        
        north = (coord.latitude - tangent_origin.latitude) * lat_scale
        east = (coord.longitude - tangent_origin.longitude) * lon_scale
        
        return north, east
        
    def _ned_to_geo(self, north: float, east: float, tangent_origin: GeoCoordinate) -> GeoCoordinate:
        """将 NED (北-东-下) 坐标转换为地理坐标"""
        # 简化版转换，仅适用于小范围区域
        lat_scale = 111320  # 每度纬度约 111320 米
        lon_scale = 111320 * math.cos(math.radians(tangent_origin.latitude))  # 根据纬度调整经度刻度
        
        lat = tangent_origin.latitude + (north / lat_scale)
        lon = tangent_origin.longitude + (east / lon_scale)
        
        return GeoCoordinate(lat, lon)
    
    def _rotate_point(self, point: Tuple[float, float], origin: Tuple[float, float], angle: float) -> Tuple[float, float]:
        """围绕原点旋转点"""
        angle_rad = math.radians(-angle)
        ox, oy = origin
        px, py = point
        
        # 平移到原点
        px -= ox
        py -= oy
        
        # 旋转
        qx = px * math.cos(angle_rad) - py * math.sin(angle_rad)
        qy = px * math.sin(angle_rad) + py * math.cos(angle_rad)
        
        # 平移回原来位置
        qx += ox
        qy += oy
        
        return qx, qy
    
    def _clamp_grid_angle90(self, grid_angle: float) -> float:
        """将网格角度限制在 -90 到 90 度，防止航线顺序翻转"""
        if grid_angle > 90.0:
            grid_angle -= 180.0
        elif grid_angle < -90.0:
            grid_angle += 180.0
        return grid_angle
    
    def _intersect_lines_with_polygon(self, lines: List[Tuple[Tuple[float, float], Tuple[float, float]]], 
                                      polygon: List[Tuple[float, float]]) -> List[Tuple[Tuple[float, float], Tuple[float, float]]]:
        """计算线段与多边形的交点"""
        result_lines = []
        
        for line in lines:
            intersections = []
            line_start, line_end = line
            
            # 计算线段与多边形所有边的交点
            for i in range(len(polygon)):
                poly_start = polygon[i]
                poly_end = polygon[(i + 1) % len(polygon)]
                
                # 检查线段是否与多边形边相交
                intersection = self._line_intersection(line_start, line_end, poly_start, poly_end)
                if intersection:
                    # 避免重复添加交点
                    if intersection not in intersections:
                        intersections.append(intersection)
            
            # 如果有两个以上交点，找出距离最远的两个点作为航线
            if len(intersections) >= 2:
                max_distance = 0
                max_points = None
                
                for i in range(len(intersections)):
                    for j in range(i + 1, len(intersections)):
                        p1, p2 = intersections[i], intersections[j]
                        # 计算点之间的距离
                        dist = math.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)
                        if dist > max_distance:
                            max_distance = dist
                            max_points = (p1, p2)
                
                if max_points:
                    result_lines.append(max_points)
        
        return result_lines
    
    def _line_intersection(self, line1_start: Tuple[float, float], line1_end: Tuple[float, float],
                         line2_start: Tuple[float, float], line2_end: Tuple[float, float]) -> Optional[Tuple[float, float]]:
        """计算两条线段的交点，如果不相交则返回 None"""
        # 线段 1: (x1, y1) 到 (x2, y2)
        x1, y1 = line1_start
        x2, y2 = line1_end
        
        # 线段 2: (x3, y3) 到 (x4, y4)
        x3, y3 = line2_start
        x4, y4 = line2_end
        
        # 计算分母
        denom = (y4 - y3) * (x2 - x1) - (x4 - x3) * (y2 - y1)
        if denom == 0:  # 平行线
            return None
            
        # 计算参数 t 和 u
        ua = ((x4 - x3) * (y1 - y3) - (y4 - y3) * (x1 - x3)) / denom
        ub = ((x2 - x1) * (y1 - y3) - (y2 - y1) * (x1 - x3)) / denom
        
        # 检查参数是否在 [0, 1] 范围内（意味着交点在两条线段上）
        if 0 <= ua <= 1 and 0 <= ub <= 1:
            # 计算交点坐标
            x = x1 + ua * (x2 - x1)
            y = y1 + ua * (y2 - y1)
            return (x, y)
        
        return None
    
    def _adjust_line_direction(self, lines: List[Tuple[Tuple[float, float], Tuple[float, float]]]) -> List[Tuple[Tuple[float, float], Tuple[float, float]]]:
        """调整线段方向保持一致"""
        if not lines:
            return []
            
        result_lines = []
        # 计算第一条线的角度作为参考
        first_line = lines[0]
        x1, y1 = first_line[0]
        x2, y2 = first_line[1]
        first_angle = math.degrees(math.atan2(y2 - y1, x2 - x1))
        
        for line in lines:
            x1, y1 = line[0]
            x2, y2 = line[1]
            line_angle = math.degrees(math.atan2(y2 - y1, x2 - x1))
            
            # 如果线段角度与第一条线相差超过 1 度，反转方向
            if abs(line_angle - first_angle) > 1.0:
                result_lines.append((line[1], line[0]))
            else:
                result_lines.append(line)
                
        return result_lines
    
    def _reverse_transect_order(self, transects: List[List[GeoCoordinate]]) -> List[List[GeoCoordinate]]:
        """反转航线顺序"""
        return transects[::-1]
    
    def _reverse_internal_transect_points(self, transects: List[List[GeoCoordinate]]) -> List[List[GeoCoordinate]]:
        """反转航线内部点顺序"""
        result = []
        for transect in transects:
            result.append(transect[::-1])
        return result
    
    def _adjust_transects_to_entry_point(self, transects: List[List[GeoCoordinate]]) -> List[List[GeoCoordinate]]:
        """根据入口点调整航线"""
        if not transects:
            return []
        
        # 复制航线以避免修改原始数据
        adjusted_transects = [list(transect) for transect in transects]
        
        reverse_points = False
        reverse_transects = False
        
        # 根据入口点位置决定是否需要反转
        if self.entry_point in [EntryLocation.BOTTOM_LEFT, EntryLocation.BOTTOM_RIGHT]:
            reverse_points = True
        if self.entry_point in [EntryLocation.TOP_RIGHT, EntryLocation.BOTTOM_RIGHT]:
            reverse_transects = True
        
        # 根据需要进行反转
        if reverse_points:
            adjusted_transects = self._reverse_internal_transect_points(adjusted_transects)
        if reverse_transects:
            adjusted_transects = self._reverse_transect_order(adjusted_transects)
            
        return adjusted_transects
    
    def _optimize_transects_for_shortest_distance(self, from_coord: GeoCoordinate, 
                                                 transects: List[List[GeoCoordinate]]) -> List[List[GeoCoordinate]]:
        """优化航线顺序以最小化距离"""
        if not transects:
            return []
            
        # 计算从 from_coord 到各个航线起点和终点的距离
        distances = [
            from_coord.distance_to(transects[0][0]),  # 到第一条航线起点
            from_coord.distance_to(transects[0][-1]), # 到第一条航线终点
            from_coord.distance_to(transects[-1][0]), # 到最后一条航线起点
            from_coord.distance_to(transects[-1][-1]) # 到最后一条航线终点
        ]
        
        # 找出最短距离的索引
        shortest_index = distances.index(min(distances))
        
        # 复制航线以避免修改原始数据
        optimized_transects = [list(transect) for transect in transects]
        
        # 根据最短距离索引调整航线
        if shortest_index > 1:  # 需要反转航线顺序
            optimized_transects = self._reverse_transect_order(optimized_transects)
        if shortest_index % 2 == 1:  # 需要反转航线内部点顺序
            optimized_transects = self._reverse_internal_transect_points(optimized_transects)
            
        return optimized_transects
    
    def generate_transects(self) -> None:
        """生成航线 - 主要函数"""
        # 清除之前的结果
        self.transects.clear()
        
        # 检查输入多边形至少有 3 个点
        if len(self.survey_area_polygon) < 3:
            return
            
        # 处理原始航线
        self._generate_transects_for_polygon(False)
        
        # 如果需要正交重飞，再次生成航线
        if self.refly_90_degrees:
            self._generate_transects_for_polygon(True)
    
    def _generate_transects_for_polygon(self, refly: bool) -> None:
        """为一个多边形生成航线"""
        # 如果没有多边形，直接返回
        if len(self.survey_area_polygon) < 3:
            return
        
        # 1. 将多边形转换为 NED 坐标
        tangent_origin = self.survey_area_polygon[0]
        polygon_points_ned = []
        for coord in self.survey_area_polygon:
            north, east = self._geo_to_ned(coord, tangent_origin)
            polygon_points_ned.append((east, north))  # 注意：NED 坐标的 y 是北向，x 是东向
        
        # 2. 计算多边形边界矩形
        xs = [p[0] for p in polygon_points_ned]
        ys = [p[1] for p in polygon_points_ned]
        min_x, max_x = min(xs), max(xs)
        min_y, max_y = min(ys), max(ys)
        
        # 计算中心点和最大宽度
        center_x = (min_x + max_x) / 2
        center_y = (min_y + max_y) / 2
        max_width = max(max_x - min_x, max_y - min_y) + 2000.0  # 额外边距确保覆盖
        half_width = max_width / 2.0
        
        # 3. 应用网格角度
        grid_angle = self._clamp_grid_angle90(self.grid_angle)
        if refly:
            grid_angle += 90  # 正交重飞
        
        # 4. 生成平行线段网格
        line_list = []
        grid_spacing = self.camera_footprint_side
        
        # 确保网格间距不会太小
        if grid_spacing < 0.5:
            grid_spacing = 100000  # 设置一个很大的值，以确保只生成一条航线
        
        # 从西到东生成平行线
        transect_x = center_x - half_width
        while transect_x < center_x + half_width:
            # 创建一条从南到北的线
            top_point = (transect_x, center_y - half_width)
            bottom_point = (transect_x, center_y + half_width)
            
            # 根据网格角度旋转线段
            rotated_top = self._rotate_point(top_point, (center_x, center_y), grid_angle)
            rotated_bottom = self._rotate_point(bottom_point, (center_x, center_y), grid_angle)
            
            line_list.append((rotated_top, rotated_bottom))
            transect_x += grid_spacing
        
        # 5. 计算线段与多边形的交点
        intersect_lines = self._intersect_lines_with_polygon(line_list, polygon_points_ned)
        
        # 6. 如果交点少于 2 个，创建一条穿过多边形中心的航线
        if len(intersect_lines) < 2:
            # 通过多边形中心的航线
            first_line = line_list[0] if line_list else None
            if first_line:
                line_center_x = (first_line[0][0] + first_line[1][0]) / 2
                line_center_y = (first_line[0][1] + first_line[1][1]) / 2
                
                center_offset_x = center_x - line_center_x
                center_offset_y = center_y - line_center_y
                
                # 移动航线到中心
                new_line = (
                    (first_line[0][0] + center_offset_x, first_line[0][1] + center_offset_y),
                    (first_line[1][0] + center_offset_x, first_line[1][1] + center_offset_y)
                )
                
                line_list = [new_line]
                intersect_lines = self._intersect_lines_with_polygon(line_list, polygon_points_ned)
        
        # 7. 确保所有线段方向一致
        result_lines = self._adjust_line_direction(intersect_lines)
        
        # 8. 将 NED 坐标转换回地理坐标
        transects = []
        for line in result_lines:
            transect = []
            # 起点
            north, east = line[0][1], line[0][0]  # 注意坐标转换
            coord = self._ned_to_geo(north, east, tangent_origin)
            # 设置海拔高度为飞行高度
            coord.altitude = self.distance_to_surface
            transect.append(coord)
            
            # 终点
            north, east = line[1][1], line[1][0]  # 注意坐标转换
            coord = self._ned_to_geo(north, east, tangent_origin)
            # 设置海拔高度为飞行高度
            coord.altitude = self.distance_to_surface
            transect.append(coord)
            
            transects.append(transect)
        
        # 9. 根据入口点位置调整航线
        transects = self._adjust_transects_to_entry_point(transects)
        
        # 10. 如果是正交重飞，优化航线最短距离
        if refly and self.transects:
            last_point = self.transects[-1][-1].coord
            transects = self._optimize_transects_for_shortest_distance(last_point, transects)
        
        # 11. 处理交错飞行
        if self.fly_alternate_transects:
            # 收集奇数和偶数航线
            even_transects = [transect for i, transect in enumerate(transects) if i % 2 == 0]
            odd_transects = [transect for i, transect in enumerate(transects) if i % 2 == 1]
            
            # 偶数航线正向，奇数航线反向
            transects = even_transects + odd_transects[::-1]
        
        # 12. 调整为草坪修剪模式 (lawnmower pattern)
        adjusted_transects = []
        reverse_vertices = False
        
        for transect in transects:
            if reverse_vertices:
                adjusted_transects.append(transect[::-1])
                reverse_vertices = False
            else:
                adjusted_transects.append(transect)
                reverse_vertices = True
        
        # 13. 转换为 CoordInfo 格式并添加到 self.transects
        for transect in adjusted_transects:
            coord_info_transect = []
            
            # 添加入口和出口点
            entry_coord_info = CoordInfo(transect[0], CoordType.SURVEY_ENTRY)
            exit_coord_info = CoordInfo(transect[1], CoordType.SURVEY_EXIT)
            
            coord_info_transect.append(entry_coord_info)
            coord_info_transect.append(exit_coord_info)
            
            # 14. 处理悬停拍照模式
            if self.hover_and_capture:
                transect_length = transect[0].distance_to(transect[1])
                transect_azimuth = transect[0].azimuth_to(transect[1])
                
                if self.camera_footprint_frontal < transect_length:
                    num_hover_points = int(math.floor(transect_length / self.camera_footprint_frontal))
                    
                    # 在航线中间添加悬停点
                    for i in range(num_hover_points):
                        hover_distance = self.camera_footprint_frontal * (i + 1)
                        hover_coord = transect[0].at_distance_and_azimuth(hover_distance, transect_azimuth)
                        hover_coord.altitude = self.distance_to_surface  # 设置海拔高度
                        hover_coord_info = CoordInfo(hover_coord, CoordType.INTERIOR_HOVER_TRIGGER)
                        
                        # 在入口点和出口点之间插入悬停点
                        coord_info_transect.insert(1 + i, hover_coord_info)
            
            # 15. 为转弯添加额外距离
            if self.turn_around_distance > 0:
                # 入口点前的转弯点
                entry_azimuth = transect[0].azimuth_to(transect[1])
                turnaround_entry = transect[0].at_distance_and_azimuth(-self.turn_around_distance, entry_azimuth)
                turnaround_entry.altitude = self.distance_to_surface  # 设置海拔高度
                turnaround_entry_info = CoordInfo(turnaround_entry, CoordType.TURNAROUND)
                coord_info_transect.insert(0, turnaround_entry_info)
                
                # 出口点后的转弯点
                exit_azimuth = transect[-1].azimuth_to(transect[-2] if len(transect) > 1 else transect[0])
                turnaround_exit = transect[-1].at_distance_and_azimuth(-self.turn_around_distance, exit_azimuth)
                turnaround_exit.altitude = self.distance_to_surface  # 设置海拔高度
                turnaround_exit_info = CoordInfo(turnaround_exit, CoordType.TURNAROUND)
                coord_info_transect.append(turnaround_exit_info)
            
            # 添加到总航线列表
            self.transects.append(coord_info_transect)
    
    def calculate_camera_shots(self) -> None:
        """计算相机拍摄次数"""
        trigger_distance = self.camera_footprint_frontal
        
        if trigger_distance == 0:
            self.camera_shots = 0
            return
            
        self.camera_shots = 0
        
        # 如果在转弯处触发相机
        if self.camera_trigger_in_turn_around:
            # 计算总飞行距离
            self.camera_shots = math.ceil(self.complex_distance / trigger_distance)
        else:
            # 根据航线计算
            for transect in self.transects:
                # 确定起点和终点
                if self.turn_around_distance > 0 and not self.hover_and_capture:
                    # 如果有转弯且不是悬停拍照，使用第二个和倒数第二个点
                    if len(transect) >= 4:
                        first_point = transect[1].coord
                        last_point = transect[-2].coord
                        transect_distance = first_point.distance_to(last_point)
                        self.camera_shots += math.ceil(transect_distance / trigger_distance)
                else:
                    # 否则使用第一个和最后一个点
                    if len(transect) >= 2:
                        first_point = transect[0].coord
                        last_point = transect[-1].coord
                        transect_distance = first_point.distance_to(last_point)
                        self.camera_shots += math.ceil(transect_distance / trigger_distance)
    
    def calculate_complex_distance(self) -> None:
        """计算总飞行距离"""
        self.complex_distance = 0
        
        # 计算所有航点之间的距离总和
        for transect in self.transects:
            for i in range(len(transect) - 1):
                self.complex_distance += transect[i].coord.distance_to(transect[i+1].coord)
            
        # 计算最后一个航线的最后一个点到下一个航线第一个点的距离
        for i in range(len(self.transects) - 1):
            last_point_current = self.transects[i][-1].coord
            first_point_next = self.transects[i+1][0].coord
            self.complex_distance += last_point_current.distance_to(first_point_next)
    
    def time_between_shots(self) -> float:
        """计算拍摄间隔时间 (秒)"""
        if self.vehicle_speed == 0:
            return 0
            
        return self.camera_footprint_frontal / self.vehicle_speed
    
    def additional_time_delay(self) -> float:
        """计算额外延迟时间 (秒)"""
        hover_time = 0
        
        # 悬停模式下的额外延迟
        if self.hover_and_capture:
            for transect in self.transects:
                hover_time += self.hover_and_capture_delay * len(transect)
                
        return hover_time

    # 新增: 任务项生成相关方法
    def append_waypoint(self, seq_num, coordinate, hold_time=0):
        """
        添加航点任务项
        
        参数:
            seq_num: 序列号
            coordinate: 坐标对象
            hold_time: 悬停时间（秒）
        
        返回:
            更新后的序列号
        """
        item = MissionItem(
            seq_num=seq_num,
            command=MavCmd.NAV_WAYPOINT,
            frame=MavFrame.GLOBAL_RELATIVE_ALT,
            param1=hold_time,  # 悬停时间
            param2=0.0,        # 接受半径（无）
            param3=0.0,        # 通过航点
            param4=float('nan'), # 偏航角（保持不变）
            latitude=coordinate.latitude,
            longitude=coordinate.longitude,
            altitude=coordinate.altitude,
            auto_continue=True,
            is_current=False
        )
        self.mission_items.append(item)
        return seq_num + 1

    def append_single_photo_capture(self, seq_num):
        """
        添加单次拍照任务项
        
        参数:
            seq_num: 序列号
        
        返回:
            更新后的序列号
        """
        item = MissionItem(
            seq_num=seq_num,
            command=MavCmd.IMAGE_START_CAPTURE,
            frame=MavFrame.MISSION,
            param1=0,          # 保留（设为0）
            param2=0,          # 间隔（无）
            param3=1,          # 拍摄1张照片
            param4=0,          # 无指定序列号
            latitude=float('nan'),
            longitude=float('nan'),
            altitude=float('nan'),
            auto_continue=True,
            is_current=False
        )
        self.mission_items.append(item)
        return seq_num + 1

    def append_exit_photo_capture(self, seq_num):
        """
        添加单次拍照任务项
        
        参数:
            seq_num: 序列号
        
        返回:
            更新后的序列号
        """
        item = MissionItem(
            seq_num=seq_num,
            command=MavCmd.DO_SET_CAM_TRIGG_DIST,
            frame=MavFrame.MISSION,
            param1=0,          # 保留（设为0）
            param2=0,          # 间隔（无）
            param3=1,          # 拍摄1张照片
            param4=0,          # 无指定序列号
            latitude=float('nan'),
            longitude=float('nan'),
            altitude=float('nan'),
            auto_continue=True,
            is_current=False
        )
        self.mission_items.append(item)
        return seq_num + 1
    def append_camera_trigger_distance(self, seq_num, trigger_distance):
        """
        添加相机触发距离任务项
        
        参数:
            seq_num: 序列号
            trigger_distance: 触发距离（米），0表示停止触发
        
        返回:
            更新后的序列号
        """
        item = MissionItem(
            seq_num=seq_num,
            command=MavCmd.DO_SET_CAM_TRIGG_DIST,
            frame=MavFrame.MISSION,
            param1=trigger_distance,
            param2=0,          # 快门集成（忽略）
            param3=1,          # 立即触发一张照片
            param4=0,          # 未使用
            latitude=float('nan'),
            longitude=float('nan'),
            altitude=float('nan'),
            auto_continue=True,
            is_current=False
        )
        self.mission_items.append(item)
        return seq_num + 1

    def append_camera_trigger_update(self, seq_num, coordinate, trigger_distance):
        """
        添加相机触发距离更新点（包括航点和触发命令）
        
        参数:
            seq_num: 序列号
            coordinate: 坐标对象
            trigger_distance: 触发距离（米）
        
        返回:
            更新后的序列号
        """
        # 先添加航点
        seq_num = self.append_waypoint(seq_num, coordinate)
        # 再添加触发距离命令
        seq_num = self.append_camera_trigger_distance(seq_num, trigger_distance)
        return seq_num

    def set_camera_calc_from_dict(self, camera_dict):
        """从字典设置相机参数"""
        # 创建相机计算对象并设置参数
        self.camera_calc = CameraCalc()
        self.camera_calc.from_dict(camera_dict)
        
        # 同步相机参数到SurveyComplexItem
        self.camera_footprint_side = self.camera_calc.adjusted_footprint_side
        self.camera_footprint_frontal = self.camera_calc.adjusted_footprint_frontal
        self.distance_to_surface = self.camera_calc.distance_to_surface
        self.camera_min_trigger_interval = self.camera_calc.min_trigger_interval

    def set_polygon_from_coords(self, polygon_coords):
        """从坐标列表设置多边形"""
        self.survey_area_polygon = []
        for coord in polygon_coords:
            lat, lon = coord
            self.survey_area_polygon.append(GeoCoordinate(lat, lon, 0))
    
    def generate_mission_items(self, start_seq_num=None):
        """
        生成任务项
        
        参数:
            start_seq_num: 起始序列号，如果为None则使用self.sequence_number
        
        返回:
            最终序列号
        """
        # 清空现有任务项
        self.mission_items.clear()
        
        # 设置起始序列号
        if start_seq_num is not None:
            self.sequence_number = start_seq_num
            
        seq_num = self.sequence_number
        
        # 遍历所有航线
        for transect_index, transect in enumerate(self.transects):
            # 遍历航线中的每个点
            for coord_index, coord_info in enumerate(transect):
                # 检查是否为最后一个航线的最后一个点
                last_point = (transect_index == len(self.transects) - 1) and (coord_index == len(transect) - 1)
                
                # 根据坐标类型处理
                if coord_info.coord_type == CoordType.TURNAROUND or coord_info.coord_type == CoordType.INTERIOR:
                    # 转弯点或内部普通点 - 只添加航点
                    seq_num = self.append_waypoint(seq_num, coord_info.coord)
                    
                elif coord_info.coord_type == CoordType.INTERIOR_HOVER_TRIGGER:
                    # 内部悬停拍照点 - 添加航点（带悬停时间）和单次拍照命令
                    seq_num = self.append_waypoint(
                        seq_num, coord_info.coord, 
                        hold_time=self.hover_and_capture_delay
                    )
                    seq_num = self.append_single_photo_capture(seq_num)
                    
                elif coord_info.coord_type == CoordType.SURVEY_ENTRY:
                    # 调查入口点
                    if not self.hover_and_capture:
                        # 距离触发模式 - 添加航点和触发距离命令（开始拍照）
                        seq_num = self.append_camera_trigger_update(
                            seq_num, coord_info.coord, 
                            self.camera_footprint_frontal
                        )
                    else:
                        # 悬停拍照模式 - 添加航点（带悬停时间）和单次拍照命令
                        seq_num = self.append_waypoint(
                            seq_num, coord_info.coord, 
                            hold_time=self.hover_and_capture_delay
                        )
                        seq_num = self.append_single_photo_capture(seq_num)
                    
                elif coord_info.coord_type == CoordType.SURVEY_EXIT:
                    # 调查出口点
                    if not self.hover_and_capture:
                        # 距离触发模式
                        # 如果是最后一个点，则停止拍照触发
                        seq_num = self.append_waypoint(seq_num, coord_info.coord)
                        # 2. 拍摄最后一张照片
                        seq_num = self.append_exit_photo_capture(seq_num)
                    else:
                        # 悬停拍照模式 - 添加航点（带悬停时间）和单次拍照命令
                        seq_num = self.append_waypoint(
                            seq_num, coord_info.coord, 
                            hold_time=self.hover_and_capture_delay
                        )
                        seq_num = self.append_single_photo_capture(seq_num)
        
        return seq_num
    
    def clear_mission_items(self):
        """清空任务项"""
        self.mission_items.clear()
        self.sequence_number = 1

            
    def export_to_json(self):
        """
        导出为与QGC兼容的JSON格式
        
        返回:
            包含完整测绘任务的JSON对象
        """
        # 构建视觉航点列表
        visual_transect_points = []
        for transect in self.transects:
            for point in transect:
                # if point.coord_type in [CoordType.SURVEY_ENTRY, CoordType.SURVEY_EXIT]:
                    visual_transect_points.append([
                        point.coord.latitude,
                        point.coord.longitude
                    ])
        
        # 构建多边形点
        polygon = [[coord.latitude, coord.longitude] for coord in self.survey_area_polygon]
        
        # 构建任务项
        items = []
        for item in self.mission_items:
            # 转换为QGC格式
            qgc_item = {
                "autoContinue": item.auto_continue,
                "command": item.command.value,
                "doJumpId": item.seq_num,
                "frame": item.frame.value,
                "params": [
                    item.param1,
                    item.param2,
                    item.param3,
                    None if math.isnan(item.param4) else item.param4,
                    item.latitude if not math.isnan(item.latitude) else 0,
                    item.longitude if not math.isnan(item.longitude) else 0,
                    item.altitude if not math.isnan(item.altitude) else 0
                ],
                "type": "SimpleItem"
            }
            items.append(qgc_item)
        
        # 构建结果JSON
        result = {
            "TransectStyleComplexItem": {
                "CameraCalc": self.camera_calc.to_dict(),
                "CameraShots": self.camera_shots,
                "CameraTriggerInTurnAround": self.camera_trigger_in_turn_around,
                "HoverAndCapture": self.hover_and_capture,
                "Items": items,
                "Refly90Degrees": self.refly_90_degrees,
                "TurnAroundDistance": self.turn_around_distance,
                "VisualTransectPoints": visual_transect_points,
                "version": 2
            },
            "angle": self.grid_angle,
            "complexItemType": "survey",
            "entryLocation": self.entry_point.value,
            "flyAlternateTransects": self.fly_alternate_transects,
            "polygon": polygon,
            "splitConcavePolygons": self.split_concave_polygons,
            "type": "ComplexItem",
            "version": 5
        }
        
        return result
        
    def export_to_json_file(self, filename):
        """
        导出为JSON文件
        
        参数:
            filename: 输出文件名
        """
        json_data = self.export_to_json()
        with open(filename, 'w') as f:
            json.dump(json_data, f, indent=4)

# 新增函数：生成测绘任务
def generate_survey_mission(polygon, camera_calc, start_seq_num=1):
    """
    生成测绘任务
    
    参数:
        polygon: 多边形顶点列表，格式为 [[lat1, lon1], [lat2, lon2], ...]
        camera_calc: 相机参数字典
        start_seq_num: 起始序列号，默认为1
    
    返回:
        (survey_item, mission_item_count): 返回生成的任务项JSON和任务项数量
    """
    # 创建 SurveyComplexItem 实例
    survey = SurveyComplexItem()
    
    # 设置相机参数
    survey.set_camera_calc_from_dict(camera_calc)
    
    # 设置多边形区域
    survey.set_polygon_from_coords(polygon)
    
    # 生成航线规划
    survey.generate_transects()
    
    # 计算性能指标
    survey.calculate_complex_distance()
    survey.calculate_camera_shots()
    
    # 生成任务项
    survey.generate_mission_items(start_seq_num=start_seq_num)
    
    # 导出到JSON格式
    survey_item = survey.export_to_json()
    item_count = len(survey.mission_items)
    
    return survey_item, item_count

# 示例使用
if __name__ == "__main__":
    # 示例多边形和相机参数
    polygon = [
        [42.295020734658124, -82.66310293006441],
        [42.29499603707458, -82.66142076013733],
        [42.29617245012069, -82.66145879956052],
        [42.296087389926804, -82.65983607385871],
        [42.29323920079205, -82.65973540149383],
        [42.29329435116038, -82.66317584085651]
    ]
    
    camera_calc = {
        "AdjustedFootprintFrontal": 21.791999999999998,
        "AdjustedFootprintSide": 65.472,
        "CameraName": "Sony ILCE-QX1",
        "DistanceMode": 1,
        "DistanceToSurface": 75.2551724137931,
        "FixedOrientation": False,
        "FocalLength": 16,
        "FrontalOverlap": 70,
        "ImageDensity": 2,
        "ImageHeight": 3632,
        "ImageWidth": 5456,
        "Landscape": True,
        "MinTriggerInterval": 0,
        "SensorHeight": 15.4,
        "SensorWidth": 23.2,
        "SideOverlap": 40,
        "ValueSetIsDistance": False,
        "version": 2
    }
    
    # 生成测绘任务
    survey_item, item_count = generate_survey_mission(polygon, camera_calc, start_seq_num=2)
    
    # 输出结果
    print(f"任务项数量: {item_count}")
    print(json.dumps(survey_item, indent=4))