#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
实验数据处理模块
用于处理experiment文件夹中的实验数据，调用drone_mission_analyzer.py进行分析，
并使用drone_mission_generator.py生成任务计划
"""

import os
import json
import time
import glob
import logging
import shutil
from typing import Dict, List, Any, Tuple, Optional
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("ExperimentProcessor")

class ExperimentProcessor:
    """实验数据处理类，用于处理experiment文件夹中的实验数据"""

    def __init__(self, experiment_dir: str = "experiment",
                 analyzer_path: str = "task_base/drone_mission_analyzer.py",
                 generator_path: str = "task_base/drone_mission_generator.py",
                 api_key: str = None,
                 model: str = None,
                 api_url: str = None):
        """
        初始化实验数据处理器

        Args:
            experiment_dir: 实验数据目录
            analyzer_path: 分析器路径
            generator_path: 生成器路径
            api_key: LLM API密钥，如果为None则使用分析器默认值
            model: LLM模型名称，如果为None则使用分析器默认值
            api_url: LLM API URL，如果为None则使用分析器默认值
        """
        self.experiment_dir = experiment_dir
        self.analyzer_path = analyzer_path
        self.generator_path = generator_path
        self.api_key = api_key
        self.model = model
        self.api_url = api_url

    def find_experiment_files(self) -> List[str]:
        """
        查找experiment文件夹中的所有exp_env_*.json文件

        Returns:
            文件路径列表
        """
        pattern = os.path.join(self.experiment_dir, "*", "exp_env_*.json")
        files = glob.glob(pattern)
        logger.info(f"找到 {len(files)} 个实验环境文件")
        return files

    def process_experiment_file(self, file_path: str) -> Tuple[Dict, float]:
        """
        处理单个实验文件

        Args:
            file_path: 实验文件路径

        Returns:
            (分析结果, 处理时间)
        """
        logger.info(f"处理实验文件: {file_path}")

        # 确定输出目录
        env_dir = os.path.dirname(file_path)
        output_dir = os.path.join(env_dir, "task_analysis")
        os.makedirs(output_dir, exist_ok=True)

        # 导入分析器模块
        sys.path.append(os.path.dirname(self.analyzer_path))

        # 读取实验文件，获取任务列表
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                env_data = json.load(f)
                user_requirements = env_data.get('user_requirements', [])
        except Exception as e:
            logger.error(f"读取实验文件失败: {str(e)}")
            raise RuntimeError(f"读取实验文件失败: {str(e)}")

        # 创建任务ID到任务的映射
        task_map = {req.get('task_id'): req for req in user_requirements}

        # 执行分析
        total_start_time = time.time()

        try:
            # 使用Python的importlib动态导入模块
            import importlib.util
            spec = importlib.util.spec_from_file_location("drone_mission_analyzer", self.analyzer_path)
            analyzer_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(analyzer_module)

            # 创建分析器实例
            analyzer = analyzer_module.DroneMissionAnalyzer(
                api_key=self.api_key if self.api_key else "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",
                model=self.model if self.model else "openai/gpt-4.1",
                base_url=self.api_url if self.api_url else "https://openrouter.ai/api/v1/"
            )

            # 调用分析函数
            logger.info(f"调用分析函数，输入文件: {file_path}，输出目录: {output_dir}")

            # 修改：我们不直接调用analyze_mission，而是手动处理每个任务并记录时间
            # 获取任务先验信息
            mission_prior = env_data.get('mission_prior', {})

            # 创建输出目录
            os.makedirs(output_dir, exist_ok=True)

            # 处理每个任务并记录时间
            task_times = {}
            results = {}

            for task in user_requirements:
                task_id = task.get('task_id')
                logger.info(f"处理任务 {task_id}")

                # 记录任务开始时间
                task_start_time = time.time()

                try:
                    # 处理单个任务
                    task_json = analyzer.process_task(task, mission_prior)

                    # 保存任务结果
                    task_file = os.path.join(output_dir, f"task_{task_id}.json")
                    with open(task_file, 'w', encoding='utf-8') as f:
                        json.dump(task_json, f, ensure_ascii=False, indent=2)

                    # 记录任务结束时间和处理时间
                    task_end_time = time.time()
                    task_processing_time = task_end_time - task_start_time
                    task_times[task_id] = task_processing_time

                    # 保存结果
                    results[task_id] = task_json

                    logger.info(f"任务 {task_id} 处理完成，耗时 {task_processing_time:.2f} 秒")
                except Exception as e:
                    logger.error(f"处理任务 {task_id} 时出错: {str(e)}")
                    # 创建错误任务结果
                    error_task = {
                        "task_id": task_id,
                        "description": task.get("description", ""),
                        "error": str(e),
                        "task_types": ["error"],
                        "task_summary": "任务处理失败。系统在分析您的请求时遇到了技术问题，无法完成任务规划。",
                        "feasibility_analysis": "处理任务时发生错误，无法确定任务可行性",
                        "subtasks": []
                    }

                    # 保存错误任务结果
                    task_file = os.path.join(output_dir, f"task_{task_id}.json")
                    with open(task_file, 'w', encoding='utf-8') as f:
                        json.dump(error_task, f, ensure_ascii=False, indent=2)

                    # 记录任务结束时间和处理时间
                    task_end_time = time.time()
                    task_processing_time = task_end_time - task_start_time
                    task_times[task_id] = task_processing_time

                    # 保存结果
                    results[task_id] = error_task

            logger.info(f"所有任务处理完成")

        except Exception as e:
            logger.error(f"分析失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            raise RuntimeError(f"分析失败: {str(e)}")

        total_end_time = time.time()
        total_processing_time = total_end_time - total_start_time

        # 将任务处理时间添加到结果中
        for task_id, task_time in task_times.items():
            if task_id in results:
                results[task_id]['processing_time'] = task_time

        return results, total_processing_time

    def generate_mission_plan(self, env_dir: str) -> Dict[str, Any]:
        """
        为指定环境目录生成任务计划

        Args:
            env_dir: 环境目录

        Returns:
            生成的任务计划
        """
        logger.info(f"为环境 {env_dir} 生成任务计划")

        # 确定输入和输出目录
        task_analysis_dir = os.path.join(env_dir, "task_analysis")
        qgc_plan_dir = os.path.join(env_dir, "QGC_plan")
        os.makedirs(qgc_plan_dir, exist_ok=True)

        # 导入生成器模块
        sys.path.append(os.path.dirname(self.generator_path))
        try:
            # 使用Python的importlib动态导入模块
            import importlib.util
            spec = importlib.util.spec_from_file_location("drone_mission_generator", self.generator_path)
            generator_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(generator_module)

            # 调用生成器函数
            logger.info(f"调用生成器函数，输入目录: {task_analysis_dir}")
            generator_module.generate_mission(task_analysis_dir)
            logger.info(f"生成完成")
        except Exception as e:
            logger.error(f"生成失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            raise RuntimeError(f"生成失败: {str(e)}")

        # 读取生成结果
        plan_files = glob.glob(os.path.join(env_dir, "task_analysis", "*.plan"))
        if not plan_files:
            # 如果在task_analysis目录中找不到plan文件，尝试在QGC_plan目录中查找
            plan_files = glob.glob(os.path.join(qgc_plan_dir, "*.plan"))


        plans = {}
        for plan_file in plan_files:
            try:
                with open(plan_file, 'r', encoding='utf-8') as f:
                    plan_id = os.path.basename(plan_file).replace(".plan", "")
                    plans[plan_id] = json.load(f)

                # 如果文件不在QGC_plan目录中，移动到QGC_plan目录
                if os.path.dirname(plan_file) != qgc_plan_dir:
                    dest_file = os.path.join(qgc_plan_dir, os.path.basename(plan_file))
                    shutil.move(plan_file, dest_file)
                    logger.info(f"已将计划文件移动到 {dest_file}")
            except Exception as e:
                logger.error(f"读取计划文件 {plan_file} 失败: {str(e)}")

        return plans

    def process_all_experiments(self) -> Dict[str, Dict]:
        """
        处理所有实验文件

        Returns:
            处理结果和时间的字典，格式为 {env_id: {results: {...}, time: float}}
        """
        experiment_files = self.find_experiment_files()
        results = {}

        for file_path in experiment_files:
            env_id = os.path.basename(os.path.dirname(file_path)).replace("env_", "")
            try:
                # 处理实验文件
                analysis_results, processing_time = self.process_experiment_file(file_path)

                # 生成任务计划
                env_dir = os.path.dirname(file_path)
                mission_plans = self.generate_mission_plan(env_dir)

                # 记录结果
                results[env_id] = {
                    "analysis_results": analysis_results,
                    "mission_plans": mission_plans,
                    "processing_time": processing_time,
                    "file_path": file_path
                }

                logger.info(f"环境 {env_id} 处理完成，耗时 {processing_time:.2f} 秒")
            except Exception as e:
                logger.error(f"处理环境 {env_id} 时出错: {str(e)}")
                results[env_id] = {
                    "error": str(e),
                    "file_path": file_path
                }

        return results

if __name__ == "__main__":
    processor = ExperimentProcessor()
    results = processor.process_all_experiments()
    print(f"处理完成，共处理 {len(results)} 个环境")
