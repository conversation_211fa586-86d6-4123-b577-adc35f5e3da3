{"mission_prior": {"home_position": [42.29617690980287, -82.6623000857478, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[42.296782914015324, -82.66550305101627], [42.297304971690316, -82.66269927977535], [42.29733241061882, -82.6598286451604], [42.29382567236911, -82.65992522198468], [42.29395755818151, -82.6657036336446]]}, "target_areas": [{"id": "area_1", "name": "warehouse", "type": "building", "geometry": {"type": "polygon", "coordinates": [[42.29561198187808, -82.66233186168357], [42.29560502916051, -82.66209174503061], [42.295534964254195, -82.66209791594007], [42.295544350934215, -82.66233698874343]]}, "properties": {"height": 10}}, {"id": "area_2", "name": "highway", "type": "road", "geometry": {"type": "linestring", "coordinates": [[42.296176362350856, -82.66354032261087], [42.296915934769096, -82.66350601350304], [42.29702832001832, -82.66238851640551]]}, "properties": {"width": 10}}, {"id": "area_3", "name": "farmland_1", "type": "agricultural", "geometry": {"type": "polygon", "coordinates": [[42.296099691680276, -82.66191966465603], [42.296078203266404, -82.66110623419834], [42.29468994161604, -82.66118279603184], [42.29471894530385, -82.66351581629775], [42.295328019677854, -82.66345700065098], [42.29530626711187, -82.66197680712521]]}}, {"id": "area_4", "name": "forest", "type": "woodland", "geometry": {"type": "polygon", "coordinates": [[42.29466064621194, -82.65989487315737], [42.29465191789008, -82.66121646253347], [42.2969256036579, -82.66115746300308], [42.29692123964465, -82.65986537341772]]}}, {"id": "area_5", "name": "farmland_2", "type": "agricultural", "geometry": {"type": "polygon", "coordinates": [[[42.29623822879315, -82.65985942042803], [42.296235523737096, -82.65864873255228], [42.294760372610845, -82.65872765937945], [42.29463911681764, -82.6599246086634]]]}}]}, "user_requirements": [{"task_id": "1", "description": "Assist in rapidly monitoring the nearby forest areas.", "description_cn": "协助监测附近的森林区域"}, {"task_id": "2", "description": "Conduct farmland surveillance and warehouse scanning.", "description_cn": "进行农田监测和仓库的建筑扫描"}, {"task_id": "3", "description": "Perform road network scanning.", "description_cn": "执行道路扫描"}, {"task_id": "4", "description": "Execute agricultural zone scanning with 70% frontal overlap, 40% lateral overlap, and a ground sampling distance (GSD) of 0.9 cm/pixel.", "description_cn": "执行农业区域扫描，前向重叠度为70%，侧向重叠度为40%，地面采样距离为0.9厘米/像素"}, {"task_id": "5", "description": "Execute sequential navigation through the following waypoints in designated order:[[42.29466064621194,-82.65989487315737],[42.29465191789008,-82.66121646253347],[42.2969256036579,-82.66115746300308],[42.29692123964465,-82.65986537341772]]", "description_cn": "按照指定顺序依次经过以下航点"}, {"task_id": "6", "description": "Perform drone missions for all designated areas in the environment.", "description_cn": "执行环境中所有指定区域的无人机任务"}]}