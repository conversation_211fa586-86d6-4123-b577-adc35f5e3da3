{"task_id": "15", "description": "Perform a polygon scan of road_2 with 80% overlap.", "home_position": [42.29617690980287, -82.6623000857478, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[42.29718194618623, -82.66589734800576], [42.29727802176437, -82.65925867248248], [42.293533588741575, -82.65931927207117], [42.29357539942106, -82.66618960254094]]}, "task_types": ["Corridor scan task"], "task_summary": "The user requested a polygon scan of 'road_2' with 80% overlap. 'road_2' is a recognized target area, and its geometry and width are available, making the task feasible. The request for 80% overlap indicates a high-precision corridor scan, which will be planned accordingly. All required parameters, including corridor width, are present. The task can be fully executed as described, with no infeasible components.", "feasibility_analysis": "The requested target area 'road_2' exists in the available target areas list and includes all necessary geometric and width data for a corridor scan. The specified overlap rate is within professional standards for high-precision mapping. There are no missing parameters or conflicting requirements, so the task is entirely feasible.", "subtasks": [{"subtask_id": "1", "type": "Corridor scan task", "target_area_id": "area_7", "target_area_name": "road_2", "feasible": true, "feasibility_reason": "'road_2' is present in the available target areas, and all required parameters (geometry, width) are provided. The specified overlap rate is supported.", "geometry": {"type": "linestring", "coordinates": [[42.29691417640369, -82.66347510875849], [42.297052834367285, -82.6612416557528]]}, "parameters": {"frontal_overlap": 80, "lateral_overlap": 70, "gsd": 1.2, "width": 10}}]}