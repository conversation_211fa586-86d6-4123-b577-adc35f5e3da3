/* 全局样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: #333;
    background-color: #f8f9fa;
}

/* 导航栏样式 */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: 600;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s, box-shadow 0.3s;
    margin-bottom: 1.5rem;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600;
}

/* 按钮样式 */
.btn {
    border-radius: 5px;
    font-weight: 500;
    padding: 0.5rem 1rem;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

.btn-success {
    background-color: #198754;
    border-color: #198754;
}

.btn-success:hover {
    background-color: #157347;
    border-color: #146c43;
}

/* 表单样式 */
.form-control {
    border-radius: 5px;
    padding: 0.75rem 1rem;
}

.form-control:focus {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* 徽章样式 */
.badge {
    padding: 0.5rem 0.75rem;
    font-weight: 500;
    border-radius: 5px;
}

.badge-feasible {
    background-color: #198754;
}

.badge-infeasible {
    background-color: #dc3545;
}

.badge-partial {
    background-color: #fd7e14;
}

/* 图标样式 */
.feature-icon {
    font-size: 2.5rem;
    color: #0d6efd;
    margin-bottom: 1rem;
}

/* 英雄区域样式 */
.hero-section {
    background-color: #f8f9fa;
    padding: 5rem 0;
    margin-bottom: 3rem;
}

/* 加载指示器样式 */
.loading {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    justify-content: center;
    align-items: center;
    color: white;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 图表容器样式 */
.chart-container {
    height: 400px;
    margin-bottom: 2rem;
}

/* 任务卡片样式 */
.task-card {
    margin-bottom: 1.5rem;
}

/* 子任务列表样式 */
.subtask-list {
    max-height: 300px;
    overflow-y: auto;
}

/* 页脚样式 */
footer {
    background-color: #f8f9fa;
    padding: 2rem 0;
    margin-top: 3rem;
    border-top: 1px solid #e9ecef;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .hero-section {
        padding: 3rem 0;
    }
    
    .chart-container {
        height: 300px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* 提示框样式 */
.toast {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.toast-header {
    border-radius: 10px 10px 0 0;
    background-color: #f8f9fa;
}

.toast-body.text-success {
    color: #198754;
}

.toast-body.text-danger {
    color: #dc3545;
}

/* 设置页面样式 */
.settings-card {
    margin-bottom: 2rem;
}

/* 导出按钮样式 */
.export-btn {
    margin-right: 0.5rem;
}

/* 摘要卡片样式 */
.summary-card {
    text-align: center;
    padding: 1.5rem;
}

.summary-card .display-4 {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* 任务类型徽章样式 */
.task-type-badge {
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}
